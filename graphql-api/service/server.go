package main

import (
	"flag"
	"fmt"
	"log"
	"pfgbulgaria/carco-graphapi/graph/directives"
	"pfgbulgaria/carco-graphapi/graph/generated"
	"pfgbulgaria/carco-graphapi/graph/resolver"
	"pfgbulgaria/carco-graphapi/pkg/cache"
	"pfgbulgaria/carco-graphapi/pkg/config"
	"pfgbulgaria/carco-graphapi/pkg/dataloaders"
	"pfgbulgaria/carco-graphapi/pkg/magento/backend/api"
	"pfgbulgaria/carco-graphapi/pkg/magento/catalog"
	"pfgbulgaria/carco-graphapi/pkg/magento/core"
	"pfgbulgaria/carco-graphapi/pkg/server"
	"pfgbulgaria/carco-graphapi/pkg/storage"
	utils2 "pfgbulgaria/carco-graphapi/pkg/utils"
	"time"

	"github.com/99designs/gqlgen/graphql/handler"
	"github.com/99designs/gqlgen/graphql/handler/transport"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func init() {
	file := flag.String("file", "base-config.yaml", "display colorized output")
	flag.Parse()

	config.LoadConfig(*file)
	storage.InitNewConnection(config.GetConfig().Database, config.GetConfig().Api.Mode)
	cache.InitNewCacheClient(config.GetConfig().Cache)

	preloadCache()
}

func preloadCache() {
	start := time.Now()
	var err error
	err = verifyConfig()
	utils2.StopOnError(err)

	err = core.PreloadAttributes()
	utils2.StopOnError(err)

	cache.DeleteKey(core.AttributeRequestVarCacheKey)
	core.GetAttributeRequestVars()

	store := core.GetStore()
	store.RefreshCache = true
	catalog.GetCategoryTree(store)

	catalog.GetCategoryProductCountMap()

	utils2.LogTimeElapsed(start, "Preload Cache")
}

func verifyConfig() error {
	_ = core.GetStore()
	table := catalog.GetCatalogTable()
	if table == "" {
		return fmt.Errorf("catalog table is empty")
	}

	return nil
}

func graphqlHandler() gin.HandlerFunc {
	conf := generated.Config{Resolvers: &resolver.Resolver{}}

	conf.Directives.HasValidCaptcha = directives.HasValidCaptcha()
	conf.Directives.HasValidCustomerToken = directives.HasValidCustomerToken()
	conf.Directives.RequestCache = directives.RequestCacheHandler()

	srv := handler.NewDefaultServer(
		generated.NewExecutableSchema(
			conf,
		))

	var mb int64 = 1 << 20
	srv.AddTransport(transport.POST{})
	srv.AddTransport(transport.MultipartForm{
		MaxMemory:     32 * mb,
		MaxUploadSize: 50 * mb,
	})
	srv2 := dataloaders.LoaderCacheMiddleware(srv)

	return func(c *gin.Context) {
		srv2.ServeHTTP(c.Writer, c.Request)
	}
}

func main() {
	//router := http.NewServeMux()
	r := gin.Default()

	allowedOrigins := []string{
		"https://carco.bg",
		"https://test.carco.bg",
	}

	if utils2.IsDebugMode() {
		allowedOrigins = []string{
			"http://localhost:3000",
			"http://localhost",
			"https://carco.bg",
			"http://carco.localhost",
			"https://test.carco.bg",
		}
	}

	r.Use(cors.New(cors.Config{
		AllowOrigins:     allowedOrigins,
		AllowMethods:     []string{"POST", "GET", "OPTION"},
		AllowHeaders:     []string{"*"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
		AllowFiles:       true,
	}))

	r.Use(server.GinContextToContextMiddleware())
	//r.Use(server.RequestLevelCache())

	if err := r.SetTrustedProxies(nil); err != nil {
		log.Fatal(err)
	}

	r.GET("/graphql", func(context *gin.Context) {
		// gin ok response
		context.JSON(200, gin.H{
			"message": "ok",
		})
	})

	r.POST("/graphql", graphqlHandler())
	r.GET("/go-api/customer/downloadOrders", api.GetCustomerFileDownloadHandler())

	r.NoRoute(func(c *gin.Context) {
		c.JSON(404, gin.H{"code": "PAGE_NOT_FOUND", "message": "Page not found: " + c.FullPath()})
	})

	conf := config.GetConfig()
	log.Printf("Conntextion statded at http://localhost:%d for GraphQL queries", conf.Api.Port)

	log.Fatal(r.Run(fmt.Sprintf(":%d", conf.Api.Port)))
}
