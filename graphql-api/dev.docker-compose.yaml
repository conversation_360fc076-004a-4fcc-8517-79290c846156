services:
  graphapi:
    container_name: carco-graphapi
    build:
      context: ./.Docker
      dockerfile: dev.Dockerfile
    user: graphapi:graphapi
    working_dir: /graphapi
    extra_hosts:
      - "db:host-gateway"
      - "cache:host-gateway"
      - "localhost:host-gateway"
    volumes:
      - ./service:/graphapi
      - .devEnv/home:/home/<USER>/:rw
      - .devEnv/.goCache:/go
    ports:
      - "9420:9420"
    networks:
      - carco-internal

networks:
  carco-internal:
    external: true
    name: carcobg_carco-internal
