<?php
// Simple debug script to check MyPOS paths
echo "MyPOS Path Debug\n";
echo "================\n\n";

// Check current working directory
echo "Current working directory: " . getcwd() . "\n";

// Check if files exist in various locations
$possiblePaths = array(
    'var/mypos/private_key.pem',
    'app/var/mypos/private_key.pem',
    '/home/<USER>/git/carco-project/carco.bg/htdocs/var/mypos/private_key.pem',
    '/home/<USER>/git/carco-project/carco.bg/htdocs/app/var/mypos/private_key.pem'
);

echo "Checking possible private key locations:\n";
foreach ($possiblePaths as $path) {
    $exists = file_exists($path);
    echo ($exists ? "✅" : "❌") . " {$path}\n";
    if ($exists) {
        $perms = substr(sprintf('%o', fileperms($path)), -4);
        echo "   Permissions: {$perms}\n";
    }
}

echo "\nChecking database configuration:\n";
try {
    $pdo = new PDO('mysql:host=carco-db;dbname=carco', 'root', 'root');
    $stmt = $pdo->prepare("SELECT value FROM core_config_data WHERE path = 'payment/pfg_mypos/private_key_path'");
    $stmt->execute();
    $configPath = $stmt->fetchColumn();
    
    echo "Database private_key_path: {$configPath}\n";
    
    // Test the full path construction
    $baseDir = '/home/<USER>/git/carco-project/carco.bg/htdocs';
    $varDir = $baseDir . '/var';
    $fullPath = $varDir . '/' . $configPath;
    
    echo "Constructed full path: {$fullPath}\n";
    echo "Full path exists: " . (file_exists($fullPath) ? "YES" : "NO") . "\n";
    
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
