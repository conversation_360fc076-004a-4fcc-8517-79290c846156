<?php
/**
 * Test MyPOS payment method directly
 */

// Simple test without full Magento bootstrap
echo "MyPOS Direct Test\n";
echo "=================\n\n";

// Test 1: Check if files exist
echo "1. Checking key files:\n";
$privateKeyPath = '/home/<USER>/git/carco-project/carco.bg/htdocs/var/mypos/private_key.pem';
$publicKeyPath = '/home/<USER>/git/carco-project/carco.bg/htdocs/var/mypos/public_key.pem';

echo "Private key: " . ($privateKeyPath) . "\n";
echo "Exists: " . (file_exists($privateKeyPath) ? "YES" : "NO") . "\n";
echo "Readable: " . (is_readable($privateKeyPath) ? "YES" : "NO") . "\n";

echo "Public key: " . ($publicKeyPath) . "\n";
echo "Exists: " . (file_exists($publicKeyPath) ? "YES" : "NO") . "\n";
echo "Readable: " . (is_readable($publicKeyPath) ? "YES" : "NO") . "\n\n";

// Test 2: Check database configuration
echo "2. Checking database configuration:\n";
try {
    $pdo = new PDO('mysql:host=carco-db;dbname=carco', 'root', 'root');
    
    $stmt = $pdo->prepare("SELECT path, value FROM core_config_data WHERE path LIKE 'payment/pfg_mypos/%' AND path IN ('payment/pfg_mypos/active', 'payment/pfg_mypos/store_id', 'payment/pfg_mypos/client_number', 'payment/pfg_mypos/private_key_path', 'payment/pfg_mypos/public_key_path') ORDER BY path");
    $stmt->execute();
    $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($configs as $config) {
        echo "✅ {$config['path']}: {$config['value']}\n";
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}

echo "\n3. Testing MyPOS SDK directly:\n";

// Test 3: Test MyPOS SDK
try {
    // Load the SDK
    require_once 'app/code/local/PFG/MyPOS/lib/MyPOS/autoload.php';
    
    echo "✅ SDK loaded successfully\n";
    
    // Create a purchase object
    $purchase = new MyPOS_SDK_Purchase();
    echo "✅ Purchase object created\n";
    
    // Set basic data
    $purchase->setStoreId('1124357')
             ->setWalletNumber('40578619575')
             ->setAmount(1000) // 10.00 BGN
             ->setCurrency('BGN')
             ->setOrderId('TEST_ORDER_123')
             ->setLanguage('BG')
             ->setUrlOk('http://example.com/success')
             ->setUrlCancel('http://example.com/cancel')
             ->setUrlNotify('http://example.com/notify');
    
    echo "✅ Purchase data set\n";
    
    // Test signature generation
    echo "Testing signature generation with absolute path...\n";
    $signature = $purchase->generateSignature($privateKeyPath);
    echo "✅ Signature generated successfully: " . substr($signature, 0, 20) . "...\n";
    
    // Test form data generation
    echo "Testing form data generation...\n";
    $formData = $purchase->getFormData($privateKeyPath, null, true); // sandbox mode
    echo "✅ Form data generated successfully\n";
    echo "URL: " . $formData['url'] . "\n";
    echo "Data fields: " . count($formData['data']) . "\n";
    
} catch (Exception $e) {
    echo "❌ SDK test failed: " . $e->getMessage() . "\n";
    echo "Error details: " . $e->getTraceAsString() . "\n";
}

echo "\n4. Testing with relative path (as Magento would):\n";

// Test 4: Test with relative path like Magento uses
try {
    // Change to Magento root directory
    chdir('/home/<USER>/git/carco-project/carco.bg/htdocs');
    echo "Changed to: " . getcwd() . "\n";
    
    $relativePath = 'var/mypos/private_key.pem';
    echo "Testing relative path: {$relativePath}\n";
    echo "File exists: " . (file_exists($relativePath) ? "YES" : "NO") . "\n";
    
    if (file_exists($relativePath)) {
        $purchase2 = new MyPOS_SDK_Purchase();
        $purchase2->setStoreId('1124357')
                  ->setWalletNumber('40578619575')
                  ->setAmount(1000)
                  ->setCurrency('BGN')
                  ->setOrderId('TEST_ORDER_456');
        
        $signature2 = $purchase2->generateSignature($relativePath);
        echo "✅ Relative path signature generated: " . substr($signature2, 0, 20) . "...\n";
    }
    
} catch (Exception $e) {
    echo "❌ Relative path test failed: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
