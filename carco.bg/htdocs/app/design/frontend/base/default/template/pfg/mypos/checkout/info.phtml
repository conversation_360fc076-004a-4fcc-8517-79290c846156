<?php
/**
 * MyPOS Payment Method Info Template
 *
 * @var $this PFG_MyPOS_Block_Checkout_Info
 */
?>
<div class="mypos-payment-info">
    <div class="mypos-header">
        <?php if ($this->getMyPOSLogoUrl()): ?>
            <img src="<?php echo $this->getMyPOSLogoUrl() ?>" alt="MyPOS" class="mypos-logo-small" />
        <?php endif; ?>
        <strong><?php echo $this->escapeHtml($this->getMethodTitle()) ?></strong>
    </div>
    
    <?php if ($this->getFormattedAmount()): ?>
        <div class="mypos-amount">
            <strong><?php echo $this->__('Amount: %s', $this->getFormattedAmount()) ?></strong>
        </div>
    <?php endif; ?>
    
    <?php $transactionData = $this->getTransactionData(); ?>
    <?php if (!empty($transactionData)): ?>
        <div class="mypos-transaction-details">
            <h4><?php echo $this->__('Transaction Details') ?></h4>
            <table class="mypos-details-table">
                <?php foreach ($transactionData as $label => $value): ?>
                    <tr>
                        <td class="label"><?php echo $this->escapeHtml($this->__($label)) ?>:</td>
                        <td class="value"><?php echo $this->escapeHtml($value) ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        </div>
    <?php endif; ?>
    
    <?php if ($this->isPaymentPending()): ?>
        <div class="mypos-status mypos-status-pending">
            <p><?php echo $this->__('Payment Status: Pending') ?></p>
            <p class="note"><?php echo $this->__('Your payment is being processed. You will receive confirmation once the payment is completed.') ?></p>
        </div>
    <?php elseif ($this->isPaymentSuccessful()): ?>
        <div class="mypos-status mypos-status-success">
            <p><?php echo $this->__('Payment Status: Successful') ?></p>
            <p class="note"><?php echo $this->__('Your payment has been processed successfully.') ?></p>
        </div>
    <?php else: ?>
        <div class="mypos-status mypos-status-failed">
            <p><?php echo $this->__('Payment Status: Failed') ?></p>
            <?php if ($this->getPaymentStatusMessage()): ?>
                <p class="note"><?php echo $this->escapeHtml($this->getPaymentStatusMessage()) ?></p>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</div>

<style>
.mypos-payment-info {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.mypos-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.mypos-logo-small {
    height: 20px;
    margin-right: 8px;
}

.mypos-amount {
    margin-bottom: 10px;
    font-size: 14px;
}

.mypos-transaction-details {
    margin-bottom: 10px;
}

.mypos-transaction-details h4 {
    margin: 0 0 8px 0;
    font-size: 13px;
    color: #333;
}

.mypos-details-table {
    width: 100%;
    border-collapse: collapse;
}

.mypos-details-table td {
    padding: 4px 8px;
    border-bottom: 1px solid #eee;
    font-size: 12px;
}

.mypos-details-table td.label {
    font-weight: bold;
    width: 40%;
    color: #666;
}

.mypos-details-table td.value {
    color: #333;
}

.mypos-status {
    padding: 8px;
    border-radius: 4px;
    margin-top: 10px;
}

.mypos-status p {
    margin: 0 0 4px 0;
    font-weight: bold;
}

.mypos-status .note {
    font-weight: normal;
    font-size: 12px;
    color: #666;
}

.mypos-status-pending {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.mypos-status-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.mypos-status-failed {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}
</style>
