<?php
/**
 * MyPOS Payment Method Form Template
 *
 * @var $this PFG_MyPOS_Block_Checkout_Form
 */
?>
<fieldset class="form-list">
    <?php $_code = $this->getMethodCode() ?>
    <ul id="payment_form_<?php echo $_code ?>" style="display:none">
        <li>
            <div class="mypos-payment-form">
                <div class="mypos-logo">
                    <?php if ($this->getMyPOSLogoUrl()): ?>
                        <img src="<?php echo $this->getMyPOSLogoUrl() ?>" alt="MyPOS" class="mypos-logo-img" />
                    <?php endif; ?>
                </div>
                
                <?php if ($this->getInstructions()): ?>
                    <div class="mypos-instructions">
                        <p><?php echo $this->escapeHtml($this->getInstructions()) ?></p>
                    </div>
                <?php endif; ?>
                
                <div class="mypos-info">
                    <p><?php echo $this->__('You will be redirected to MyPOS secure payment page to complete your payment.') ?></p>
                    <p><?php echo $this->__('Supported cards: Visa, MasterCard, Maestro, V Pay') ?></p>
                </div>
                
                <?php if ($this->isSandboxMode()): ?>
                    <div class="mypos-sandbox-notice">
                        <p style="color: #ff6600; font-weight: bold;">
                            <?php echo $this->__('SANDBOX MODE: This is a test environment. No real payments will be processed.') ?>
                        </p>
                    </div>
                <?php endif; ?>
                
                <?php if (!$this->isCurrencySupported()): ?>
                    <div class="mypos-currency-warning">
                        <p style="color: #ff0000; font-weight: bold;">
                            <?php echo $this->__('Warning: Current currency (%s) may not be supported by MyPOS. Supported currencies: %s', 
                                $this->getQuoteCurrency(), 
                                implode(', ', $this->getSupportedCurrencies())
                            ) ?>
                        </p>
                    </div>
                <?php endif; ?>
            </div>
        </li>
    </ul>
</fieldset>

<style>
.mypos-payment-form {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.mypos-logo {
    text-align: center;
    margin-bottom: 10px;
}

.mypos-logo-img {
    max-height: 40px;
    max-width: 150px;
}

.mypos-instructions {
    margin-bottom: 10px;
    padding: 8px;
    background-color: #e7f3ff;
    border-left: 4px solid #2196F3;
}

.mypos-info {
    margin-bottom: 10px;
}

.mypos-info p {
    margin: 5px 0;
    font-size: 13px;
    color: #666;
}

.mypos-sandbox-notice {
    margin-bottom: 10px;
    padding: 8px;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
}

.mypos-currency-warning {
    margin-bottom: 10px;
    padding: 8px;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
}
</style>
