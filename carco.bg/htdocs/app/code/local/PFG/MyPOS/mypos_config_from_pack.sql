-- MyPOS Configuration (Generated from Configuration Pack)
-- Generated on: 2025-07-30 18:11:38

-- Enable MyPOS payment method
INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/active', '1') ON DUPLICATE KEY UPDATE value = '1';

-- Set basic configuration
INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/title', 'MyPOS Credit Card Payment') ON DUPLICATE KEY UPDATE value = 'MyPOS Credit Card Payment';
INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/instructions', 'You will be redirected to MyPOS secure payment page') ON DUPLICATE KEY UPDATE value = 'You will be redirected to MyPOS secure payment page';

-- Set MyPOS credentials
INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/store_id', '1124357') ON DUPLICATE KEY UPDATE value = '1124357';
INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/client_number', '40578619575') ON DUPLICATE KEY UPDATE value = '40578619575';

-- Set Key Index
INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/key_index', '1') ON DUPLICATE KEY UPDATE value = '1';

-- Set key file paths
INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/private_key_path', 'mypos/private_key.pem') ON DUPLICATE KEY UPDATE value = 'mypos/private_key.pem';
INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/public_key_path', 'mypos/public_key.pem') ON DUPLICATE KEY UPDATE value = 'mypos/public_key.pem';

-- Set default configuration
INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/currency', 'BGN') ON DUPLICATE KEY UPDATE value = 'BGN';
INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/language', 'BG') ON DUPLICATE KEY UPDATE value = 'BG';
INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/sandbox_mode', '1') ON DUPLICATE KEY UPDATE value = '1';
INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/new_order_status', 'pending') ON DUPLICATE KEY UPDATE value = 'pending';
INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/order_status_after_payment', 'processing') ON DUPLICATE KEY UPDATE value = 'processing';
INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/sort_order', '110') ON DUPLICATE KEY UPDATE value = '110';

-- Verify configuration
SELECT path, value FROM core_config_data WHERE path LIKE 'payment/pfg_mypos/%' ORDER BY path;
