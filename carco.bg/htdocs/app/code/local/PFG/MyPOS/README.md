# MyPOS Payment Gateway for Magento 1.9

A complete payment method integration for MyPOS payment gateway that works across the full stack: Magento 1.9 backend, GraphQL API middleware, and React frontend.

## Features

- **Full Stack Integration**: Works seamlessly across Magento backend, GraphQL API, and React frontend
- **Secure Payments**: Uses MyPOS secure payment gateway with SSL encryption
- **Multiple Currencies**: Supports BGN, EUR, and USD
- **Sandbox Mode**: Test mode for development and testing
- **Transaction Management**: Complete transaction tracking and status management
- **Admin Interface**: Admin panel for viewing and managing MyPOS transactions
- **Automatic Status Updates**: Cron job for checking payment statuses
- **Refund Support**: Full and partial refund capabilities
- **Responsive Design**: Mobile-friendly payment forms

## Requirements

- Magento 1.9.x
- PHP 5.4.8 or later
- cURL extension
- OpenSSL extension
- MyPOS merchant account
- SSL certificate (required for production)

## Installation

### 1. Module Installation

The module files are already installed in the following structure:
```
app/code/local/PFG/MyPOS/
app/etc/modules/PFG_MyPOS.xml
app/design/frontend/base/default/template/pfg/mypos/
app/design/frontend/base/default/layout/pfg/mypos/
```

### 2. Database Setup

Run the database installation script:
```bash
# The database tables will be created automatically when you access the admin panel
# Or run manually:
php -f shell/indexer.php reindexall
```

### 3. Clear Cache

Clear Magento cache:
```bash
rm -rf var/cache/*
rm -rf var/session/*
```

### 4. Validation

Run the installation validation script:
```bash
cd app/code/local/PFG/MyPOS/Test/
php validate_installation.php
```

## Configuration

### 1. MyPOS Account Setup

1. Register for a MyPOS merchant account at https://mypos.com/
2. Obtain your credentials:
   - Store ID (SID)
   - Wallet Number
   - Private Key file
   - Public Key file

### 2. Magento Admin Configuration

1. Go to **System > Configuration > Payment Methods**
2. Find **MyPOS Payment Gateway** section
3. Configure the following settings:

#### Basic Settings
- **Enabled**: Yes
- **Title**: MyPOS Credit Card Payment
- **Instructions**: Payment instructions for customers
- **Description**: Description shown on MyPOS checkout page

#### Order Status Settings
- **New Order Status**: pending
- **Order Status After Payment**: processing
- **Payment Failed CMS Page**: Select a CMS page for failed payments

#### MyPOS API Settings
- **Store ID (SID)**: Your MyPOS Store ID
- **Wallet Number**: Your MyPOS Wallet Number
- **Currency**: BGN/EUR/USD (must be agreed with MyPOS)
- **Language**: BG/EN

#### Security Settings
- **Private Key Path**: Path to private key file (relative to var/ folder)
- **Private Key Password**: Password for private key (if required)
- **Public Key Path**: Path to public key file (relative to var/ folder)
- **Sandbox Mode**: Yes for testing, No for production

#### Advanced Settings
- **Sort Order**: 110 (or desired position)

### 3. Key Files Setup

1. Upload your MyPOS private and public key files to the `var/` directory
2. Set appropriate file permissions (600 for private key)
3. Update the paths in the configuration

Example:
```
var/mypos/private_key.pem (chmod 600)
var/mypos/public_key.pem (chmod 644)
```

### 4. GraphQL API Configuration

The GraphQL API integration is automatically configured. The payment method mapping includes:
- Frontend type: `mypos`
- Magento method: `pfg_mypos`

### 5. React Frontend Configuration

The React frontend integration is automatically configured with:
- Payment option: "MyPOS плащане"
- Payment type: `mypos`
- Information component with payment details

## Usage

### Customer Payment Flow

1. Customer selects MyPOS payment method during checkout
2. Customer is redirected to secure MyPOS payment page
3. Customer enters card details and completes payment
4. Customer is redirected back to success/failure page
5. Order status is updated automatically

### Admin Management

#### View Transactions
- Go to **Sales > MyPOS Transactions**
- View all MyPOS transactions with status, amounts, and dates
- Click on transaction to view details

#### Check Payment Status
- Go to **Sales > Orders**
- Open a MyPOS order
- Click "Check MyPOS Payment Status" button

#### Process Refunds
- Go to **Sales > Orders**
- Open a paid MyPOS order
- Click "MyPOS Refund" button
- Or use standard Magento credit memo process

### Cron Jobs

The module includes a cron job that runs every 10 minutes to check pending payment statuses:
```xml
<cron_expr>*/10 * * * *</cron_expr>
```

## Testing

### Sandbox Mode

1. Enable sandbox mode in configuration
2. Use MyPOS test credentials
3. Test payments will not be processed for real

### Test Cards

Use MyPOS provided test card numbers for sandbox testing.

### Unit Tests

Run the included unit tests:
```bash
cd app/code/local/PFG/MyPOS/Test/
phpunit PaymentMethodTest.php
phpunit TransactionTest.php
phpunit SDKTest.php
```

## Troubleshooting

### Common Issues

1. **Payment method not visible**
   - Check if module is enabled
   - Verify configuration settings
   - Clear cache

2. **Signature validation fails**
   - Check private/public key files
   - Verify file permissions
   - Ensure correct key format

3. **Redirect not working**
   - Check SSL certificate
   - Verify callback URLs
   - Check server logs

4. **Cron not running**
   - Verify Magento cron is configured
   - Check cron logs
   - Run manually: `php cron.php`

### Logs

Check the following log files:
- `var/log/pfg_mypos.log` - General MyPOS logs
- `var/log/pfg_mypos_exceptions.log` - Exception logs
- `var/log/system.log` - Magento system logs

### Debug Mode

Enable debug logging in configuration:
- **System > Configuration > Advanced > Developer > Log Settings**
- Set **Enabled** to Yes

## Security

- Private keys should have 600 permissions
- Use HTTPS for all callback URLs
- Regularly update the module
- Monitor transaction logs

## Support

For technical support:
1. Check the troubleshooting section
2. Review log files
3. Run the validation script
4. Contact MyPOS support for API issues

## License

This module is provided as-is for integration with MyPOS payment gateway.

## Changelog

### Version 0.1.0
- Initial release
- Full stack integration (Magento + GraphQL + React)
- Complete payment flow implementation
- Admin interface and transaction management
- Cron job for status updates
- Refund support
- Comprehensive testing suite
