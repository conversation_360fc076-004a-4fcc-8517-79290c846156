<?php
/**
 * MyPOS Configuration Pack Parser
 * 
 * This script helps you parse the MyPOS Configuration Pack and extract
 * the private key, public key, Store ID, and Client Number
 */

require_once dirname(__FILE__) . '/../../../../Mage.php';
Mage::app();

class MyPOS_ConfigPack_Parser
{
    /**
     * Parse MyPOS Configuration Pack
     * 
     * @param string $configPack The configuration pack string from MyPOS
     * @return array Parsed configuration data
     */
    public function parseConfigPack($configPack)
    {
        echo "MyPOS Configuration Pack Parser\n";
        echo "==============================\n\n";
        
        if (empty($configPack)) {
            echo "❌ Error: Configuration Pack is empty\n";
            echo "\nTo get your Configuration Pack:\n";
            echo "1. Log into your MyPOS merchant account\n";
            echo "2. Go to 'Stores' menu\n";
            echo "3. Find your eCommerce store (or create one)\n";
            echo "4. Click 'Configure' button in Integration tab\n";
            echo "5. Select 'Custom Integration Method'\n";
            echo "6. Choose 'Configuration Pack' option\n";
            echo "7. Copy the generated pack string\n\n";
            return false;
        }
        
        try {
            // Configuration pack is typically a base64 encoded JSON string
            // or a specially formatted string containing all the credentials
            
            echo "📦 Parsing Configuration Pack...\n";
            echo "Pack length: " . strlen($configPack) . " characters\n\n";
            
            // Try to decode as base64 first
            $decoded = base64_decode($configPack, true);
            if ($decoded !== false) {
                echo "✅ Successfully decoded base64 data\n";
                
                // Try to parse as JSON
                $jsonData = json_decode($decoded, true);
                if ($jsonData !== null) {
                    echo "✅ Successfully parsed JSON data\n";
                    return $this->extractFromJson($jsonData);
                } else {
                    echo "ℹ️  Not JSON format, trying other parsing methods...\n";
                    return $this->extractFromString($decoded);
                }
            } else {
                echo "ℹ️  Not base64 encoded, parsing as plain text...\n";
                return $this->extractFromString($configPack);
            }
            
        } catch (Exception $e) {
            echo "❌ Error parsing configuration pack: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * Extract configuration from JSON data
     */
    private function extractFromJson($jsonData)
    {
        echo "📋 Extracting data from JSON format...\n\n";
        
        $config = array();
        
        // Common field mappings for MyPOS config packs
        $fieldMappings = array(
            'private_key' => array('private_key', 'privateKey', 'privKey', 'key_private'),
            'public_key' => array('public_key', 'publicKey', 'pubKey', 'key_public', 'certificate'),
            'store_id' => array('store_id', 'storeId', 'SID', 'sid', 'store'),
            'client_number' => array('client_number', 'clientNumber', 'wallet_number', 'walletNumber', 'client'),
            'key_index' => array('key_index', 'keyIndex', 'index')
        );
        
        foreach ($fieldMappings as $configKey => $possibleKeys) {
            foreach ($possibleKeys as $key) {
                if (isset($jsonData[$key])) {
                    $config[$configKey] = $jsonData[$key];
                    echo "✅ Found {$configKey}: " . substr($jsonData[$key], 0, 50) . "...\n";
                    break;
                }
            }
        }
        
        return $this->validateAndSaveConfig($config);
    }
    
    /**
     * Extract configuration from string data
     */
    private function extractFromString($data)
    {
        echo "📋 Extracting data from string format...\n\n";
        
        $config = array();
        
        // Look for PEM formatted keys
        if (preg_match('/-----BEGIN RSA PRIVATE KEY-----(.*?)-----END RSA PRIVATE KEY-----/s', $data, $matches)) {
            $config['private_key'] = "-----BEGIN RSA PRIVATE KEY-----" . $matches[1] . "-----END RSA PRIVATE KEY-----";
            echo "✅ Found private key\n";
        }
        
        if (preg_match('/-----BEGIN PUBLIC KEY-----(.*?)-----END PUBLIC KEY-----/s', $data, $matches)) {
            $config['public_key'] = "-----BEGIN PUBLIC KEY-----" . $matches[1] . "-----END PUBLIC KEY-----";
            echo "✅ Found public key\n";
        }
        
        if (preg_match('/-----BEGIN CERTIFICATE-----(.*?)-----END CERTIFICATE-----/s', $data, $matches)) {
            $config['public_key'] = "-----BEGIN CERTIFICATE-----" . $matches[1] . "-----END CERTIFICATE-----";
            echo "✅ Found public certificate\n";
        }
        
        // Look for Store ID and Client Number patterns
        // These are typically alphanumeric strings of specific lengths
        $lines = explode("\n", $data);
        foreach ($lines as $line) {
            $line = trim($line);
            
            // Store ID pattern (usually shorter)
            if (preg_match('/^[A-Za-z0-9]{20,30}$/', $line) && !isset($config['store_id'])) {
                $config['store_id'] = $line;
                echo "✅ Found potential Store ID: {$line}\n";
            }
            
            // Client Number pattern (usually longer)
            if (preg_match('/^[A-Za-z0-9]{40,60}$/', $line) && !isset($config['client_number'])) {
                $config['client_number'] = $line;
                echo "✅ Found potential Client Number: {$line}\n";
            }
            
            // Key Index pattern
            if (preg_match('/^[0-9]+$/', $line) && strlen($line) <= 3 && !isset($config['key_index'])) {
                $config['key_index'] = $line;
                echo "✅ Found potential Key Index: {$line}\n";
            }
        }
        
        return $this->validateAndSaveConfig($config);
    }
    
    /**
     * Validate and save configuration
     */
    private function validateAndSaveConfig($config)
    {
        echo "\n🔍 Validating configuration...\n";
        
        $required = array('private_key', 'public_key', 'store_id', 'client_number');
        $missing = array();
        
        foreach ($required as $field) {
            if (empty($config[$field])) {
                $missing[] = $field;
            }
        }
        
        if (!empty($missing)) {
            echo "❌ Missing required fields: " . implode(', ', $missing) . "\n";
            echo "\nFound fields:\n";
            foreach ($config as $key => $value) {
                if (!empty($value)) {
                    echo "  ✅ {$key}: " . substr($value, 0, 50) . "...\n";
                }
            }
            return false;
        }
        
        echo "✅ All required fields found!\n\n";
        
        // Save keys to files
        $this->saveKeysToFiles($config);
        
        // Generate Magento configuration
        $this->generateMagentoConfig($config);
        
        return $config;
    }
    
    /**
     * Save keys to files
     */
    private function saveKeysToFiles($config)
    {
        echo "💾 Saving keys to files...\n";
        
        $varDir = Mage::getBaseDir('var');
        $myposDir = $varDir . DS . 'mypos';
        
        // Create directory if it doesn't exist
        if (!is_dir($myposDir)) {
            mkdir($myposDir, 0755, true);
            echo "✅ Created directory: {$myposDir}\n";
        }
        
        // Save private key
        $privateKeyPath = $myposDir . DS . 'private_key.pem';
        file_put_contents($privateKeyPath, $config['private_key']);
        chmod($privateKeyPath, 0600);
        echo "✅ Saved private key: {$privateKeyPath}\n";
        
        // Save public key
        $publicKeyPath = $myposDir . DS . 'public_key.pem';
        file_put_contents($publicKeyPath, $config['public_key']);
        chmod($publicKeyPath, 0644);
        echo "✅ Saved public key: {$publicKeyPath}\n";
        
        echo "\n";
    }
    
    /**
     * Generate Magento configuration
     */
    private function generateMagentoConfig($config)
    {
        echo "⚙️  Generating Magento configuration...\n";
        
        $configSql = "-- MyPOS Configuration (Generated from Configuration Pack)\n\n";
        $configSql .= "-- Enable MyPOS payment method\n";
        $configSql .= "INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/active', '1') ON DUPLICATE KEY UPDATE value = '1';\n\n";
        
        $configSql .= "-- Set Store ID\n";
        $configSql .= "INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/store_id', '{$config['store_id']}') ON DUPLICATE KEY UPDATE value = '{$config['store_id']}';\n\n";
        
        $configSql .= "-- Set Client Number\n";
        $configSql .= "INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/client_number', '{$config['client_number']}') ON DUPLICATE KEY UPDATE value = '{$config['client_number']}';\n\n";
        
        if (!empty($config['key_index'])) {
            $configSql .= "-- Set Key Index\n";
            $configSql .= "INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/key_index', '{$config['key_index']}') ON DUPLICATE KEY UPDATE value = '{$config['key_index']}';\n\n";
        }
        
        $configSql .= "-- Set key paths\n";
        $configSql .= "INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/private_key_path', 'mypos/private_key.pem') ON DUPLICATE KEY UPDATE value = 'mypos/private_key.pem';\n";
        $configSql .= "INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/public_key_path', 'mypos/public_key.pem') ON DUPLICATE KEY UPDATE value = 'mypos/public_key.pem';\n\n";
        
        $configSql .= "-- Enable sandbox mode for testing\n";
        $configSql .= "INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/sandbox_mode', '1') ON DUPLICATE KEY UPDATE value = '1';\n\n";
        
        $configFile = dirname(__FILE__) . DS . 'mypos_config_from_pack.sql';
        file_put_contents($configFile, $configSql);
        
        echo "✅ Generated SQL configuration: {$configFile}\n";
        echo "\n🎉 Configuration Pack parsed successfully!\n\n";
        
        echo "📋 Summary:\n";
        echo "  Store ID: {$config['store_id']}\n";
        echo "  Client Number: {$config['client_number']}\n";
        if (!empty($config['key_index'])) {
            echo "  Key Index: {$config['key_index']}\n";
        }
        echo "  Private Key: Saved to var/mypos/private_key.pem\n";
        echo "  Public Key: Saved to var/mypos/public_key.pem\n";
        echo "\n📝 Next Steps:\n";
        echo "1. Run the generated SQL file to configure Magento\n";
        echo "2. Configure remaining settings in Magento admin\n";
        echo "3. Test in sandbox mode\n";
        echo "4. Go live!\n";
    }
}

// Usage example
echo "MyPOS Configuration Pack Parser\n";
echo "==============================\n\n";
echo "To use this script:\n";
echo "1. Get your Configuration Pack from MyPOS account\n";
echo "2. Run: php parse_config_pack.php 'YOUR_CONFIG_PACK_STRING'\n\n";

if (isset($argv[1]) && !empty($argv[1])) {
    $parser = new MyPOS_ConfigPack_Parser();
    $parser->parseConfigPack($argv[1]);
} else {
    echo "Usage: php parse_config_pack.php 'YOUR_CONFIG_PACK_STRING'\n";
    echo "\nExample:\n";
    echo "php parse_config_pack.php 'eyJzdG9yZV9pZCI6IkhTZERROGRTbDlNVnpvTWJHeGdSVkx5dCIsImNsaWVudF9udW1iZXIiOiJEZnFFUElpY3dTWlBsN2hRdzRPTmF5NlNsRTZCVXRBaktrbkJ1MERWSklkd3NyWkIiLCJwcml2YXRlX2tleSI6Ii0tLS0tQkVHSU4gUlNBIFBSSVZBVEUgS0VZLS0tLS1cblxuLS0tLS1FTkQgUlNBIFBSSVZBVEUgS0VZLS0tLS0iLCJwdWJsaWNfa2V5IjoiLS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS1cblxuLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tIn0='\n";
}
