<?php

class PFG_MyPOS_Model_System_Config_Source_Order_Status_All
{
    /**
     * Options getter
     *
     * @return array
     */
    public function toOptionArray()
    {
        $statuses = Mage::getSingleton('sales/order_config')->getStatuses();
        $options = array();
        
        foreach ($statuses as $code => $label) {
            $options[] = array(
                'value' => $code,
                'label' => $label
            );
        }
        
        return $options;
    }

    /**
     * Get options in "key-value" format
     *
     * @return array
     */
    public function toArray()
    {
        return Mage::getSingleton('sales/order_config')->getStatuses();
    }
}
