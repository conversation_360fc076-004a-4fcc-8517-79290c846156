<?php

class PFG_MyPOS_Model_System_Config_Source_Currency
{
    /**
     * Options getter
     *
     * @return array
     */
    public function toOptionArray()
    {
        return array(
            array('value' => 'BGN', 'label' => Mage::helper('pfg_mypos')->__('Bulgarian Lev (BGN)')),
            array('value' => 'EUR', 'label' => Mage::helper('pfg_mypos')->__('Euro (EUR)')),
            array('value' => 'USD', 'label' => Mage::helper('pfg_mypos')->__('US Dollar (USD)')),
        );
    }

    /**
     * Get options in "key-value" format
     *
     * @return array
     */
    public function toArray()
    {
        return array(
            'BGN' => Mage::helper('pfg_mypos')->__('Bulgarian Lev (BGN)'),
            'EUR' => Mage::helper('pfg_mypos')->__('Euro (EUR)'),
            'USD' => Mage::helper('pfg_mypos')->__('US Dollar (USD)'),
        );
    }
}
