<?php

class PFG_MyPOS_Model_Observer
{
    /**
     * Change order status after MyPOS order placement
     *
     * @param Varien_Event_Observer $observer
     */
    public function myposChangeOrderStatus(Varien_Event_Observer $observer)
    {
        $order = $observer->getEvent()->getOrder();
        
        if (!$order || !$order->getId()) {
            return;
        }
        
        $payment = $order->getPayment();
        if (!$payment || $payment->getMethod() !== PFG_MyPOS_Model_Payment_Mypos::PAYMENT_METHOD_CODE) {
            return;
        }
        
        try {
            $helper = Mage::helper('pfg_mypos');
            $newOrderStatus = $helper->getNewOrderStatus();
            
            if ($newOrderStatus && $newOrderStatus !== $order->getStatus()) {
                $order->setStatus($newOrderStatus)
                      ->addStatusHistoryComment(
                          $helper->__('Order status changed to %s after MyPOS payment initiation.', $newOrderStatus),
                          $newOrderStatus
                      )
                      ->save();
                      
                $helper->log('Order status changed to ' . $newOrderStatus . ' for order #' . $order->getIncrementId());
            }
            
        } catch (Exception $e) {
            Mage::helper('pfg_mypos')->logException($e);
        }
    }
}
