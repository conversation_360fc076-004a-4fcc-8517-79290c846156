<?php

class PFG_MyPOS_Model_Adminhtml_Observer
{
    /**
     * Add check payment status button to order view
     *
     * @param Varien_Event_Observer $observer
     */
    public function addCheckPaymentStatusButton(Varien_Event_Observer $observer)
    {
        $block = $observer->getEvent()->getBlock();
        
        if ($block instanceof Mage_Adminhtml_Block_Sales_Order_View) {
            $order = $block->getOrder();
            
            if ($order && $order->getPayment()->getMethod() === PFG_MyPOS_Model_Payment_Mypos::PAYMENT_METHOD_CODE) {
                $helper = Mage::helper('pfg_mypos');
                
                // Only show button if payment is not yet confirmed
                if (!$this->isPaymentConfirmed($order)) {
                    $url = Mage::helper('adminhtml')->getUrl('adminhtml/mypos/checkPaymentStatus', array(
                        'order_id' => $order->getId()
                    ));
                    
                    $block->addButton('mypos_check_status', array(
                        'label'     => $helper->__('Check MyPOS Payment Status'),
                        'onclick'   => 'setLocation(\'' . $url . '\')',
                        'class'     => 'go'
                    ));
                }
                
                // Add refund button if payment is successful and not fully refunded
                if ($this->canRefund($order)) {
                    $url = Mage::helper('adminhtml')->getUrl('adminhtml/mypos/refund', array(
                        'order_id' => $order->getId()
                    ));
                    
                    $block->addButton('mypos_refund', array(
                        'label'     => $helper->__('MyPOS Refund'),
                        'onclick'   => 'confirmSetLocation(\'' . $helper->__('Are you sure you want to refund this payment?') . '\', \'' . $url . '\')',
                        'class'     => 'delete'
                    ));
                }
            }
        }
    }
    
    /**
     * Check if payment is confirmed
     *
     * @param Mage_Sales_Model_Order $order
     * @return bool
     */
    protected function isPaymentConfirmed($order)
    {
        $payment = $order->getPayment();
        $myposTransactionId = $payment->getAdditionalInformation('mypos_transaction_id');
        $status = $payment->getAdditionalInformation('mypos_status');
        
        return !empty($myposTransactionId) && $status === '00';
    }
    
    /**
     * Check if order can be refunded via MyPOS
     *
     * @param Mage_Sales_Model_Order $order
     * @return bool
     */
    protected function canRefund($order)
    {
        if (!$this->isPaymentConfirmed($order)) {
            return false;
        }
        
        // Check if order has been invoiced
        if (!$order->hasInvoices()) {
            return false;
        }
        
        // Check if there are any credit memos (partial refunds)
        $totalRefunded = $order->getTotalRefunded();
        $grandTotal = $order->getGrandTotal();
        
        // Can refund if not fully refunded
        return $totalRefunded < $grandTotal;
    }
}
