<?php

class PFG_MyPOS_Model_Payment_Mypos extends Mage_Payment_Model_Method_Abstract
{
    const PAYMENT_METHOD_CODE = 'pfg_mypos';

    protected $_code = self::PAYMENT_METHOD_CODE;

    protected $_canCapture = true;
    protected $_canRefund = true;
    protected $_canRefundInvoicePartial = true;
    protected $_canVoid = false;
    protected $_canUseInternal = false;
    protected $_canUseCheckout = true;
    protected $_canUseForMultishipping = false;

    protected $_formBlockType = 'pfg_mypos/checkout_form';
    protected $_infoBlockType = 'pfg_mypos/checkout_info';

    /**
     * Get order place redirect URL
     *
     * @return string
     */
    public function getOrderPlaceRedirectUrl()
    {
        return Mage::getUrl('pfg_mypos/request/redirect');
    }

    /**
     * Get instructions text from config
     *
     * @return string
     */
    public function getInstructions()
    {
        return trim($this->getConfigData('instructions'));
    }

    /**
     * Check whether payment method can be used
     *
     * @param Mage_Sales_Model_Quote|null $quote
     * @return bool
     */
    public function isAvailable($quote = null)
    {
        if (!parent::isAvailable($quote)) {
            return false;
        }

        // Check if required configuration is set
        if (!$this->getConfigData('store_id') ||
            !$this->getConfigData('client_number') ||
            !$this->getConfigData('private_key_path') ||
            !$this->getConfigData('public_key_path')) {
            return false;
        }

        return true;
    }

    /**
     * Capture payment
     *
     * @param Varien_Object $payment
     * @param float $amount
     * @return $this
     */
    public function capture(Varien_Object $payment, $amount)
    {
        $order = $payment->getOrder();
        
        // For MyPOS, capture is handled automatically after successful payment
        // This method is called when the payment is confirmed
        
        $payment->setTransactionId($payment->getAdditionalInformation('mypos_transaction_id'))
                ->setIsTransactionClosed(0);

        return $this;
    }

    /**
     * Refund payment
     *
     * @param Varien_Object $payment
     * @param float $amount
     * @return $this
     */
    public function refund(Varien_Object $payment, $amount)
    {
        $myposHelper = Mage::helper('pfg_mypos/mypos');
        $order = $payment->getOrder();
        
        if (!is_object($order) || !$order->getId()) {
            Mage::throwException($myposHelper->__('Could not get order from payment.'));
        }

        try {
            $refundRequest = $myposHelper->getRefundRequest($order, $amount);
            $refundResult = $myposHelper->processRefund($refundRequest);
            
            if (!$refundResult['success']) {
                Mage::throwException($refundResult['message']);
            }

            $payment->setTransactionId($refundResult['transaction_id'])
                    ->setIsTransactionClosed(1);

        } catch (Exception $e) {
            Mage::logException($e);
            Mage::throwException($myposHelper->__('Refund failed: %s', $e->getMessage()));
        }

        return $this;
    }

    /**
     * Get MyPOS configuration data
     *
     * @param string $field
     * @param int|string|null|Mage_Core_Model_Store $storeId
     * @return mixed
     */
    public function getConfigData($field, $storeId = null)
    {
        if (null === $storeId) {
            $storeId = $this->getStore();
        }
        
        $path = 'payment/' . $this->getCode() . '/' . $field;
        return Mage::getStoreConfig($path, $storeId);
    }

    /**
     * Check if sandbox mode is enabled
     *
     * @return bool
     */
    public function isSandboxMode()
    {
        return (bool)$this->getConfigData('sandbox_mode');
    }

    /**
     * Get MyPOS store ID
     *
     * @return string
     */
    public function getStoreId()
    {
        return $this->getConfigData('store_id');
    }

    /**
     * Get client number
     *
     * @return string
     */
    public function getClientNumber()
    {
        return $this->getConfigData('client_number');
    }

    /**
     * Get MyPOS wallet number (alias for client number for backward compatibility)
     *
     * @return string
     */
    public function getWalletNumber()
    {
        return $this->getClientNumber();
    }

    /**
     * Get currency for MyPOS
     *
     * @return string
     */
    public function getCurrency()
    {
        return $this->getConfigData('currency') ?: 'BGN';
    }

    /**
     * Get language for MyPOS interface
     *
     * @return string
     */
    public function getLanguage()
    {
        return $this->getConfigData('language') ?: 'BG';
    }

    /**
     * Get private key file path
     *
     * @return string
     */
    public function getPrivateKeyPath()
    {
        $configPath = $this->getConfigData('private_key_path');

        // If it's already an absolute path, use it as-is
        if (substr($configPath, 0, 1) === '/' || substr($configPath, 1, 1) === ':') {
            return $configPath;
        }

        // Otherwise, construct relative to var directory
        return Mage::getBaseDir('var') . DS . $configPath;
    }

    /**
     * Get public key file path
     *
     * @return string
     */
    public function getPublicKeyPath()
    {
        $configPath = $this->getConfigData('public_key_path');

        // If it's already an absolute path, use it as-is
        if (substr($configPath, 0, 1) === '/' || substr($configPath, 1, 1) === ':') {
            return $configPath;
        }

        // Otherwise, construct relative to var directory
        return Mage::getBaseDir('var') . DS . $configPath;
    }

    /**
     * Get private key password
     *
     * @return string
     */
    public function getPrivateKeyPassword()
    {
        return $this->getConfigData('private_key_password');
    }
}
