<?php

class PFG_MyPOS_Model_Cron
{
    /**
     * Check payment statuses for pending orders
     */
    public function checkOrdersPaymentStatuses()
    {
        $helper = Mage::helper('pfg_mypos');
        $helper->log('Starting MyPOS payment status check cron job');
        
        try {
            $collection = Mage::getModel('pfg_mypos/transaction')
                ->getCollection()
                ->getPendingTransactions()
                ->setPageSize(50);
            
            $processedCount = 0;
            $updatedCount = 0;
            
            foreach ($collection as $transaction) {
                try {
                    $this->checkTransactionStatus($transaction);
                    $processedCount++;
                    
                    if ($transaction->getStatus() !== PFG_MyPOS_Model_Transaction::STATUS_PENDING) {
                        $updatedCount++;
                    }
                    
                } catch (Exception $e) {
                    $helper->logException($e);
                    $helper->log('Error checking transaction ' . $transaction->getId() . ': ' . $e->getMessage());
                }
            }
            
            $helper->log("MyPOS cron job completed. Processed: {$processedCount}, Updated: {$updatedCount}");
            
        } catch (Exception $e) {
            $helper->logException($e);
            $helper->log('Error in MyPOS cron job: ' . $e->getMessage());
        }
    }
    
    /**
     * Check individual transaction status
     *
     * @param PFG_MyPOS_Model_Transaction $transaction
     */
    public function checkTransactionStatus($transaction)
    {
        $helper = Mage::helper('pfg_mypos');
        $myposHelper = Mage::helper('pfg_mypos/mypos');
        
        $order = $transaction->getOrder();
        if (!$order) {
            $helper->log('Order not found for transaction ' . $transaction->getId());
            return;
        }
        
        try {
            // Get payment status from MyPOS
            $statusData = $this->getPaymentStatusFromMyPOS($transaction);
            
            if (!$statusData) {
                $helper->log('Could not get status for transaction ' . $transaction->getId());
                return;
            }
            
            $status = $statusData['Status'];
            $statusMessage = isset($statusData['StatusMsg']) ? $statusData['StatusMsg'] : '';
            
            // Update transaction based on status
            switch ($status) {
                case '00': // Success
                    $this->handleSuccessfulPayment($transaction, $order, $statusData);
                    break;
                    
                case '01':
                case '02':
                case '03': // Cancelled/Timeout/Declined
                    $this->handleCancelledPayment($transaction, $order, $statusData);
                    break;
                    
                default: // Failed
                    $this->handleFailedPayment($transaction, $order, $statusData);
                    break;
            }
            
        } catch (Exception $e) {
            $helper->logException($e);
            throw $e;
        }
    }
    
    /**
     * Get payment status from MyPOS API
     *
     * @param PFG_MyPOS_Model_Transaction $transaction
     * @return array|false
     */
    protected function getPaymentStatusFromMyPOS($transaction)
    {
        $helper = Mage::helper('pfg_mypos');
        $paymentMethod = $helper->getPaymentMethod();
        $myposHelper = Mage::helper('pfg_mypos/mypos');
        
        $requestData = array(
            'IPCmethod' => 'IPCGetPaymentStatus',
            'IPCVersion' => '1.4',
            'SID' => $paymentMethod->getStoreId(),
            'WalletNumber' => $paymentMethod->getWalletNumber(),
            'OrderID' => $transaction->getOrderReference(),
            'OutputFormat' => 'json'
        );
        
        // Add signature
        $requestData['Signature'] = $myposHelper->generateSignature($requestData);
        
        // Make API call
        $apiUrl = $myposHelper->getApiUrl() . 'IPCGetPaymentStatus';
        return $myposHelper->makeApiCall($apiUrl, $requestData);
    }
    
    /**
     * Handle successful payment
     *
     * @param PFG_MyPOS_Model_Transaction $transaction
     * @param Mage_Sales_Model_Order $order
     * @param array $statusData
     */
    protected function handleSuccessfulPayment($transaction, $order, $statusData)
    {
        $helper = Mage::helper('pfg_mypos');
        
        // Update transaction
        $transaction->markAsSuccessful($statusData['IPC_Trnref'], $statusData);
        
        // Update order payment
        $payment = $order->getPayment();
        $payment->setAdditionalInformation('mypos_transaction_id', $statusData['IPC_Trnref'])
                ->setAdditionalInformation('mypos_status', $statusData['Status'])
                ->setAdditionalInformation('mypos_status_message', isset($statusData['StatusMsg']) ? $statusData['StatusMsg'] : '')
                ->setTransactionId($statusData['IPC_Trnref'])
                ->setIsTransactionClosed(0)
                ->save();
        
        // Update order status
        $orderStatusAfterPayment = $helper->getOrderStatusAfterPayment();
        $order->setStatus($orderStatusAfterPayment)
              ->addStatusHistoryComment(
                  $helper->__('Payment confirmed by MyPOS. Transaction ID: %s', $statusData['IPC_Trnref']),
                  $orderStatusAfterPayment
              )
              ->save();
        
        $helper->log('Payment confirmed for order #' . $order->getIncrementId() . ', transaction: ' . $statusData['IPC_Trnref']);
    }
    
    /**
     * Handle cancelled payment
     *
     * @param PFG_MyPOS_Model_Transaction $transaction
     * @param Mage_Sales_Model_Order $order
     * @param array $statusData
     */
    protected function handleCancelledPayment($transaction, $order, $statusData)
    {
        $helper = Mage::helper('pfg_mypos');
        
        // Update transaction
        $transaction->markAsCancelled($statusData);
        
        // Update order payment
        $payment = $order->getPayment();
        $payment->setAdditionalInformation('mypos_status', $statusData['Status'])
                ->setAdditionalInformation('mypos_status_message', isset($statusData['StatusMsg']) ? $statusData['StatusMsg'] : '')
                ->save();
        
        // Cancel order
        $order->cancel()
              ->addStatusHistoryComment(
                  $helper->__('Payment cancelled by customer or timed out. Status: %s', $statusData['Status'])
              )
              ->save();
        
        $helper->log('Payment cancelled for order #' . $order->getIncrementId() . ', status: ' . $statusData['Status']);
    }
    
    /**
     * Handle failed payment
     *
     * @param PFG_MyPOS_Model_Transaction $transaction
     * @param Mage_Sales_Model_Order $order
     * @param array $statusData
     */
    protected function handleFailedPayment($transaction, $order, $statusData)
    {
        $helper = Mage::helper('pfg_mypos');
        $errorMessage = isset($statusData['StatusMsg']) ? $statusData['StatusMsg'] : 'Payment failed';
        
        // Update transaction
        $transaction->markAsFailed($errorMessage, $statusData);
        
        // Update order payment
        $payment = $order->getPayment();
        $payment->setAdditionalInformation('mypos_status', $statusData['Status'])
                ->setAdditionalInformation('mypos_status_message', $errorMessage)
                ->save();
        
        // Cancel order
        $order->cancel()
              ->addStatusHistoryComment(
                  $helper->__('Payment failed. Error: %s', $errorMessage)
              )
              ->save();
        
        $helper->log('Payment failed for order #' . $order->getIncrementId() . ', error: ' . $errorMessage);
    }
}
