<?php

class PFG_MyPOS_Model_Transaction extends Mage_Core_Model_Abstract
{
    const STATUS_PENDING = 'pending';
    const STATUS_SUCCESS = 'success';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_REFUNDED = 'refunded';

    protected function _construct()
    {
        $this->_init('pfg_mypos/transaction');
    }

    /**
     * Get transaction by order reference
     *
     * @param string $orderReference
     * @return PFG_MyPOS_Model_Transaction
     */
    public function loadByOrderReference($orderReference)
    {
        return $this->load($orderReference, 'order_reference');
    }

    /**
     * Get transaction by order increment ID
     *
     * @param string $incrementId
     * @return PFG_MyPOS_Model_Transaction
     */
    public function loadByOrderIncrementId($incrementId)
    {
        return $this->load($incrementId, 'order_increment_id');
    }

    /**
     * Get transaction by MyPOS transaction ID
     *
     * @param string $myposTransactionId
     * @return PFG_MyPOS_Model_Transaction
     */
    public function loadByMyposTransactionId($myposTransactionId)
    {
        return $this->load($myposTransactionId, 'mypos_transaction_id');
    }

    /**
     * Check if transaction is successful
     *
     * @return bool
     */
    public function isSuccessful()
    {
        return $this->getStatus() === self::STATUS_SUCCESS;
    }

    /**
     * Check if transaction is pending
     *
     * @return bool
     */
    public function isPending()
    {
        return $this->getStatus() === self::STATUS_PENDING;
    }

    /**
     * Check if transaction is failed
     *
     * @return bool
     */
    public function isFailed()
    {
        return $this->getStatus() === self::STATUS_FAILED;
    }

    /**
     * Check if transaction is cancelled
     *
     * @return bool
     */
    public function isCancelled()
    {
        return $this->getStatus() === self::STATUS_CANCELLED;
    }

    /**
     * Check if transaction is refunded
     *
     * @return bool
     */
    public function isRefunded()
    {
        return $this->getStatus() === self::STATUS_REFUNDED;
    }

    /**
     * Mark transaction as successful
     *
     * @param string $myposTransactionId
     * @param array $responseData
     * @return $this
     */
    public function markAsSuccessful($myposTransactionId, $responseData = array())
    {
        $this->setStatus(self::STATUS_SUCCESS)
             ->setMyposTransactionId($myposTransactionId)
             ->setResponseData(json_encode($responseData))
             ->setUpdatedAt(now())
             ->save();

        return $this;
    }

    /**
     * Mark transaction as failed
     *
     * @param string $errorMessage
     * @param array $responseData
     * @return $this
     */
    public function markAsFailed($errorMessage, $responseData = array())
    {
        $this->setStatus(self::STATUS_FAILED)
             ->setErrorMessage($errorMessage)
             ->setResponseData(json_encode($responseData))
             ->setUpdatedAt(now())
             ->save();

        return $this;
    }

    /**
     * Mark transaction as cancelled
     *
     * @param array $responseData
     * @return $this
     */
    public function markAsCancelled($responseData = array())
    {
        $this->setStatus(self::STATUS_CANCELLED)
             ->setResponseData(json_encode($responseData))
             ->setUpdatedAt(now())
             ->save();

        return $this;
    }

    /**
     * Mark transaction as refunded
     *
     * @param float $refundAmount
     * @param string $refundTransactionId
     * @return $this
     */
    public function markAsRefunded($refundAmount, $refundTransactionId)
    {
        $this->setStatus(self::STATUS_REFUNDED)
             ->setRefundAmount($refundAmount)
             ->setRefundTransactionId($refundTransactionId)
             ->setUpdatedAt(now())
             ->save();

        return $this;
    }

    /**
     * Get order associated with this transaction
     *
     * @return Mage_Sales_Model_Order|false
     */
    public function getOrder()
    {
        if ($this->getOrderId()) {
            $order = Mage::getModel('sales/order')->load($this->getOrderId());
            return $order->getId() ? $order : false;
        }
        
        return false;
    }

    /**
     * Get decoded request data
     *
     * @return array
     */
    public function getDecodedRequestData()
    {
        $requestData = $this->getRequestData();
        return $requestData ? json_decode($requestData, true) : array();
    }

    /**
     * Get decoded response data
     *
     * @return array
     */
    public function getDecodedResponseData()
    {
        $responseData = $this->getResponseData();
        return $responseData ? json_decode($responseData, true) : array();
    }
}
