<?php

class PFG_MyPOS_Model_System_Config_Source_Language
{
    /**
     * Options getter
     *
     * @return array
     */
    public function toOptionArray()
    {
        return array(
            array('value' => 'BG', 'label' => Mage::helper('pfg_mypos')->__('Bulgarian (BG)')),
            array('value' => 'EN', 'label' => Mage::helper('pfg_mypos')->__('English (EN)')),
        );
    }

    /**
     * Get options in "key-value" format
     *
     * @return array
     */
    public function toArray()
    {
        return array(
            'BG' => Mage::helper('pfg_mypos')->__('Bulgarian (BG)'),
            'EN' => Mage::helper('pfg_mypos')->__('English (EN)'),
        );
    }
}
