# How to Get MyPOS Configuration Pack (EASIEST METHOD!)

## 🎯 Configuration Pack - All-in-One Solution

The **Configuration Pack** is a single string that contains everything you need:
- ✅ Private Key
- ✅ Public Key
- ✅ Store ID
- ✅ Client Number
- ✅ Key Index

**No need to get separate files!** 🎉

## 📍 How to Get Your Configuration Pack

### Step-by-Step Instructions:

1. **Log into your MyPOS merchant account**
   - Go to https://mypos.com/ and sign in

2. **Navigate to Stores**
   - Click on **"Stores"** in the main menu

3. **Find or Create Your eCommerce Store**
   - If you already have a store, find it in the list
   - If not, click **"Create store"** and set it up

4. **Access Integration Settings**
   - Click the **"Configure"** button in the Integration tab of your store

5. **Select Custom Integration Method**
   - Choose **"Custom Integration Method"**
   - Select **"Configuration Pack"** option

6. **Generate Configuration Pack**
   - Click **"Generate Configuration Pack"**
   - Copy the generated string (it will be long!)

### What the Configuration Pack Looks Like:
```
eyJzdG9yZV9pZCI6IkhTZERROGRTbDlNVnpvTWJHeGdSVkx5dCIsImNsaWVudF9udW1iZXIiOiJEZnFFUElpY3dTWlBsN2hRdzRPTmF5NlNsRTZCVXRBaktrbkJ1MERWSklkd3NyWkIiLCJwcml2YXRlX2tleSI6Ii0tLS0tQkVHSU4gUlNBIFBSSVZBVEUgS0VZLS0tLS1cblxuLS0tLS1FTkQgUlNBIFBSSVZBVEUgS0VZLS0tLS0iLCJwdWJsaWNfa2V5IjoiLS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS1cblxuLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tIn0=
```
*(This is just an example - yours will be different)*

### Method 3: Contact MyPOS Support
If you can't find the key generation options:

**Email**: <EMAIL>
**Website**: https://mypos.com/contact

**What to tell them**:
```
Subject: Need RSA Keys for API Integration

Hello,

I need to generate RSA keys for MyPOS API integration with my e-commerce website.

My account details:
- Client Number: HSdDQ8dSl9MVzoMbGxgRVLyt
- Client Code: DfqEPIicwSZPl7hQw4ONay6SlE6BUtAjKgnBu0DVJIdwsrZB
- Date: 07.06.2025

According to your documentation, I need:
1. RSA Private Key (2048-bit, PEM format)
2. RSA Public Key (2048-bit, PEM format)
3. MyPOS Public Certificate

Please guide me to the key generation section in my account or provide the Configuration Pack.

Thank you.
```

## 🔧 How to Use Your Configuration Pack

Once you have the Configuration Pack string:

1. **Parse the Configuration Pack**:
   ```bash
   cd /home/<USER>/git/carco-project/carco.bg/htdocs/app/code/local/PFG/MyPOS/
   php parse_config_pack.php 'YOUR_CONFIG_PACK_STRING_HERE'
   ```

2. **The script will automatically**:
   - Extract private and public keys
   - Save them to `var/mypos/private_key.pem` and `var/mypos/public_key.pem`
   - Extract Store ID and Client Number
   - Generate SQL configuration for Magento
   - Set proper file permissions

## 🔧 Configuration After Getting Keys

1. **Run the setup script**:
   ```bash
   cd app/code/local/PFG/MyPOS/
   php setup_mypos_config.php
   ```

2. **Configure in Magento Admin**:
   - Go to **System > Configuration > Payment Methods**
   - Find **MyPOS Payment Gateway**
   - Enter your credentials:
     - Store ID: `HSdDQ8dSl9MVzoMbGxgRVLyt` (verify with MyPOS)
     - Wallet Number: `DfqEPIicwSZPl7hQw4ONay6SlE6BUtAjKgnBu0DVJIdwsrZB` (verify with MyPOS)
     - Private Key Path: `mypos/private_key.pem`
     - Public Key Path: `mypos/public_key.pem`

3. **Test the integration**:
   ```bash
   cd app/code/local/PFG/MyPOS/Test/
   php validate_installation.php
   ```

## ⚠️ Important Notes

- **Never share your private key** - keep it secure
- **Use sandbox mode** for testing before going live
- **Verify the Store ID and Wallet Number mapping** with MyPOS support
- **Test thoroughly** before processing real payments

## 🚀 Alternative: Generate Your Own Keys (Advanced)

If MyPOS allows self-generated keys, you can create them:

```bash
# Generate private key
openssl genrsa -out private_key.pem 2048

# Generate public key from private key
openssl rsa -in private_key.pem -pubout -out public_key.pem
```

**Note**: You would then need to upload the public key to MyPOS and they would need to approve it.

## 📋 Checklist

- [ ] Contact MyPOS support for keys
- [ ] Download private and public key files
- [ ] Upload keys to `var/mypos/` directory
- [ ] Set correct file permissions
- [ ] Verify Store ID vs Wallet Number mapping
- [ ] Configure payment method in Magento admin
- [ ] Test in sandbox mode
- [ ] Run validation script
- [ ] Test complete payment flow

## 🆘 Troubleshooting

If you encounter issues:

1. **Check file permissions** - private key must be 600
2. **Verify file format** - must be valid PEM format
3. **Check paths** - ensure paths in config match actual file locations
4. **Test keys** - use OpenSSL to verify key validity:
   ```bash
   openssl rsa -in var/mypos/private_key.pem -check
   openssl rsa -pubin -in var/mypos/public_key.pem -text
   ```

Once you have the keys, the MyPOS payment method will be fully functional!
