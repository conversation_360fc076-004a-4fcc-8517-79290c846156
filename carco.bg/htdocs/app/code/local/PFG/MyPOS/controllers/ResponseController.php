<?php

class PFG_MyPOS_ResponseController extends Mage_Core_Controller_Front_Action
{
    /**
     * Handle successful payment response
     */
    public function successAction()
    {
        try {
            $helper = Mage::helper('pfg_mypos');
            $response = MyPOS_SDK_Response::fromPost();
            
            $helper->log('Received success response from MyPOS: ' . json_encode($response->getData()));
            
            // Validate response signature
            $paymentMethod = $helper->getPaymentMethod();
            if (!$response->validateSignature($paymentMethod->getPublicKeyPath())) {
                throw new Exception('Invalid response signature');
            }
            
            if (!$response->isSuccessful()) {
                throw new Exception('Payment was not successful: ' . $response->getErrorMessage());
            }
            
            $orderReference = $response->getOrderId();
            $transaction = Mage::getModel('pfg_mypos/transaction')->loadByOrderReference($orderReference);
            
            if (!$transaction->getId()) {
                throw new Exception('Transaction not found for order reference: ' . $orderReference);
            }
            
            $order = $transaction->getOrder();
            if (!$order) {
                throw new Exception('Order not found for transaction');
            }
            
            // Update transaction and order
            $this->processSuccessfulPayment($transaction, $order, $response);
            
            $helper->log('Payment processed successfully for order #' . $order->getIncrementId());
            
            // Redirect to success page
            $this->_redirect('checkout/onepage/success');
            
        } catch (Exception $e) {
            Mage::helper('pfg_mypos')->logException($e);
            
            $session = Mage::getSingleton('checkout/session');
            $session->addError($this->__('Payment processing failed. Please contact support.'));
            
            $this->_redirect('checkout/cart');
        }
    }
    
    /**
     * Handle payment cancellation
     */
    public function cancelAction()
    {
        try {
            $helper = Mage::helper('pfg_mypos');
            $response = MyPOS_SDK_Response::fromPost();
            
            $helper->log('Received cancel response from MyPOS: ' . json_encode($response->getData()));
            
            $orderReference = $response->getOrderId();
            if ($orderReference) {
                $transaction = Mage::getModel('pfg_mypos/transaction')->loadByOrderReference($orderReference);
                
                if ($transaction->getId()) {
                    $transaction->markAsCancelled($response->getData());
                    
                    $order = $transaction->getOrder();
                    if ($order) {
                        $order->cancel()
                              ->addStatusHistoryComment($helper->__('Payment cancelled by customer'))
                              ->save();
                    }
                }
            }
            
            $session = Mage::getSingleton('checkout/session');
            $session->addError($this->__('Payment was cancelled.'));
            
            // Restore quote
            $this->restoreQuote();
            
        } catch (Exception $e) {
            Mage::helper('pfg_mypos')->logException($e);
        }
        
        $this->_redirect('checkout/cart');
    }
    
    /**
     * Handle payment notification (IPN)
     */
    public function notifyAction()
    {
        try {
            $helper = Mage::helper('pfg_mypos');
            $response = MyPOS_SDK_Response::fromPost();
            
            $helper->log('Received notify response from MyPOS: ' . json_encode($response->getData()));
            
            // Validate response signature
            $paymentMethod = $helper->getPaymentMethod();
            if (!$response->validateSignature($paymentMethod->getPublicKeyPath())) {
                $helper->log('Invalid signature in notify response', Zend_Log::ERR);
                $this->getResponse()->setHttpResponseCode(400);
                return;
            }
            
            $orderReference = $response->getOrderId();
            $transaction = Mage::getModel('pfg_mypos/transaction')->loadByOrderReference($orderReference);
            
            if (!$transaction->getId()) {
                $helper->log('Transaction not found for order reference: ' . $orderReference, Zend_Log::ERR);
                $this->getResponse()->setHttpResponseCode(404);
                return;
            }
            
            $order = $transaction->getOrder();
            if (!$order) {
                $helper->log('Order not found for transaction', Zend_Log::ERR);
                $this->getResponse()->setHttpResponseCode(404);
                return;
            }
            
            // Process the notification
            if ($response->isSuccessful()) {
                $this->processSuccessfulPayment($transaction, $order, $response);
            } else {
                $this->processFailedPayment($transaction, $order, $response);
            }
            
            // Send OK response to MyPOS
            $this->getResponse()
                 ->setHttpResponseCode(200)
                 ->setBody('OK');
            
        } catch (Exception $e) {
            Mage::helper('pfg_mypos')->logException($e);
            $this->getResponse()->setHttpResponseCode(500);
        }
    }
    
    /**
     * Process successful payment
     *
     * @param PFG_MyPOS_Model_Transaction $transaction
     * @param Mage_Sales_Model_Order $order
     * @param MyPOS_SDK_Response $response
     */
    protected function processSuccessfulPayment($transaction, $order, $response)
    {
        $helper = Mage::helper('pfg_mypos');
        
        // Update transaction
        $transaction->markAsSuccessful($response->getTransactionReference(), $response->getData());
        
        // Update order payment
        $payment = $order->getPayment();
        $payment->setAdditionalInformation('mypos_transaction_id', $response->getTransactionReference())
                ->setAdditionalInformation('mypos_order_reference', $response->getOrderId())
                ->setAdditionalInformation('mypos_status', $response->getStatus())
                ->setAdditionalInformation('mypos_status_message', $response->getStatusMessage())
                ->setTransactionId($response->getTransactionReference())
                ->setIsTransactionClosed(0)
                ->save();
        
        // Update order status
        $orderStatusAfterPayment = $helper->getOrderStatusAfterPayment();
        $order->setStatus($orderStatusAfterPayment)
              ->addStatusHistoryComment(
                  $helper->__('Payment confirmed by MyPOS. Transaction ID: %s', $response->getTransactionReference()),
                  $orderStatusAfterPayment
              )
              ->save();
        
        // Send order confirmation email if not sent yet
        if (!$order->getEmailSent()) {
            $order->sendNewOrderEmail();
            $order->setEmailSent(true)->save();
        }
    }
    
    /**
     * Process failed payment
     *
     * @param PFG_MyPOS_Model_Transaction $transaction
     * @param Mage_Sales_Model_Order $order
     * @param MyPOS_SDK_Response $response
     */
    protected function processFailedPayment($transaction, $order, $response)
    {
        $helper = Mage::helper('pfg_mypos');
        $errorMessage = $response->getErrorMessage();
        
        // Update transaction
        $transaction->markAsFailed($errorMessage, $response->getData());
        
        // Update order payment
        $payment = $order->getPayment();
        $payment->setAdditionalInformation('mypos_status', $response->getStatus())
                ->setAdditionalInformation('mypos_status_message', $errorMessage)
                ->save();
        
        // Cancel order
        $order->cancel()
              ->addStatusHistoryComment($helper->__('Payment failed. Error: %s', $errorMessage))
              ->save();
    }
    
    /**
     * Restore quote after payment failure
     */
    protected function restoreQuote()
    {
        $session = Mage::getSingleton('checkout/session');
        $order = Mage::getModel('sales/order')->loadByIncrementId($session->getLastRealOrderId());
        
        if ($order->getId()) {
            $quote = Mage::getModel('sales/quote')->load($order->getQuoteId());
            if ($quote->getId()) {
                $quote->setIsActive(1)->save();
                $session->setQuoteId($quote->getId());
            }
        }
    }
}
