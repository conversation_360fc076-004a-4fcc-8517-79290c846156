<?php

class PFG_MyPOS_RequestController extends Mage_Core_Controller_Front_Action
{
    /**
     * Redirect to MyPOS payment gateway
     */
    public function redirectAction()
    {
        try {
            $session = Mage::getSingleton('checkout/session');
            $order = Mage::getModel('sales/order')->loadByIncrementId($session->getLastRealOrderId());
            
            if (!$order->getId()) {
                Mage::throwException('Order not found');
            }
            
            $helper = Mage::helper('pfg_mypos');
            
            if (!$helper->canProcessOrder($order)) {
                Mage::throwException('Order cannot be processed with MyPOS');
            }
            
            $myposHelper = Mage::helper('pfg_mypos/mypos');
            $formData = $myposHelper->createPurchaseRequest($order);
            
            $helper->log('Redirecting to MyPOS for order #' . $order->getIncrementId());
            
            // Render form that auto-submits to MyPOS
            $this->loadLayout();
            $this->getLayout()->getBlock('content')->append(
                $this->getLayout()->createBlock('core/template')
                    ->setTemplate('pfg/mypos/redirect.phtml')
                    ->setFormData($formData)
            );
            $this->renderLayout();
            
        } catch (Exception $e) {
            Mage::helper('pfg_mypos')->logException($e);
            
            $session = Mage::getSingleton('checkout/session');
            $session->addError($this->__('Payment initialization failed. Please try again.'));
            
            $this->_redirect('checkout/cart');
        }
    }
    
    /**
     * Handle payment cancellation
     */
    public function cancelAction()
    {
        try {
            $helper = Mage::helper('pfg_mypos');
            $helper->log('Payment cancelled by user');
            
            $session = Mage::getSingleton('checkout/session');
            $session->addError($this->__('Payment was cancelled. Please try again.'));
            
            // Restore quote
            $this->restoreQuote();
            
        } catch (Exception $e) {
            Mage::helper('pfg_mypos')->logException($e);
        }
        
        $this->_redirect('checkout/cart');
    }
    
    /**
     * Restore quote after payment cancellation
     */
    protected function restoreQuote()
    {
        $session = Mage::getSingleton('checkout/session');
        $order = Mage::getModel('sales/order')->loadByIncrementId($session->getLastRealOrderId());
        
        if ($order->getId()) {
            $quote = Mage::getModel('sales/quote')->load($order->getQuoteId());
            if ($quote->getId()) {
                $quote->setIsActive(1)->save();
                $session->setQuoteId($quote->getId());
            }
        }
    }
}
