<?php

class PFG_MyPOS_Block_Adminhtml_Transaction_Renderer_Order extends Mage_Adminhtml_Block_Widget_Grid_Column_Renderer_Abstract
{
    public function render(Varien_Object $row)
    {
        $orderIncrementId = $row->getData('order_increment_id');
        $orderId = $row->getData('order_id');
        
        if ($orderIncrementId && $orderId) {
            $url = $this->getUrl('adminhtml/sales_order/view', array('order_id' => $orderId));
            return sprintf('<a href="%s" target="_blank">%s</a>', $url, $this->escapeHtml($orderIncrementId));
        }
        
        return $this->escapeHtml($orderIncrementId);
    }
}
