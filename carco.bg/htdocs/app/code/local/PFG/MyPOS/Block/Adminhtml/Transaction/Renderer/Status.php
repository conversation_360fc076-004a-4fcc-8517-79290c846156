<?php

class PFG_MyPOS_Block_Adminhtml_Transaction_Renderer_Status extends Mage_Adminhtml_Block_Widget_Grid_Column_Renderer_Abstract
{
    public function render(Varien_Object $row)
    {
        $status = $row->getData('status');
        $statusLabels = array(
            PFG_MyPOS_Model_Transaction::STATUS_PENDING => array('label' => 'Pending', 'class' => 'grid-severity-minor'),
            PFG_MyPOS_Model_Transaction::STATUS_SUCCESS => array('label' => 'Success', 'class' => 'grid-severity-notice'),
            PFG_MyPOS_Model_Transaction::STATUS_FAILED => array('label' => 'Failed', 'class' => 'grid-severity-critical'),
            PFG_MyPOS_Model_Transaction::STATUS_CANCELLED => array('label' => 'Cancelled', 'class' => 'grid-severity-major'),
            PFG_MyPOS_Model_Transaction::STATUS_REFUNDED => array('label' => 'Refunded', 'class' => 'grid-severity-minor'),
        );
        
        if (isset($statusLabels[$status])) {
            $statusInfo = $statusLabels[$status];
            return sprintf(
                '<span class="%s"><span>%s</span></span>',
                $statusInfo['class'],
                $this->escapeHtml(Mage::helper('pfg_mypos')->__($statusInfo['label']))
            );
        }
        
        return $this->escapeHtml($status);
    }
}
