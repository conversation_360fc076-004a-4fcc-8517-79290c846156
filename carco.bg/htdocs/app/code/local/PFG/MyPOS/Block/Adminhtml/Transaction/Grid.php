<?php

class PFG_MyPOS_Block_Adminhtml_Transaction_Grid extends Mage_Adminhtml_Block_Widget_Grid
{
    public function __construct()
    {
        parent::__construct();
        $this->setId('mypos_transaction_grid');
        $this->setDefaultSort('created_at');
        $this->setDefaultDir('DESC');
        $this->setSaveParametersInSession(true);
    }

    protected function _prepareCollection()
    {
        $collection = Mage::getModel('pfg_mypos/transaction')->getCollection();
        
        // Join with sales_flat_order to get order information
        $collection->getSelect()->joinLeft(
            array('order' => $collection->getTable('sales/order')),
            'main_table.order_id = order.entity_id',
            array(
                'order_increment_id' => 'order.increment_id',
                'order_status' => 'order.status',
                'customer_email' => 'order.customer_email',
                'grand_total' => 'order.grand_total'
            )
        );
        
        $this->setCollection($collection);
        return parent::_prepareCollection();
    }

    protected function _prepareColumns()
    {
        $this->addColumn('transaction_id', array(
            'header'    => Mage::helper('pfg_mypos')->__('Transaction ID'),
            'align'     => 'right',
            'width'     => '50px',
            'index'     => 'transaction_id',
            'type'      => 'number',
        ));

        $this->addColumn('order_increment_id', array(
            'header'    => Mage::helper('pfg_mypos')->__('Order #'),
            'align'     => 'left',
            'index'     => 'order_increment_id',
            'renderer'  => 'pfg_mypos/adminhtml_transaction_renderer_order',
        ));

        $this->addColumn('order_reference', array(
            'header'    => Mage::helper('pfg_mypos')->__('Order Reference'),
            'align'     => 'left',
            'index'     => 'order_reference',
        ));

        $this->addColumn('mypos_transaction_id', array(
            'header'    => Mage::helper('pfg_mypos')->__('MyPOS Transaction ID'),
            'align'     => 'left',
            'index'     => 'mypos_transaction_id',
        ));

        $this->addColumn('amount', array(
            'header'    => Mage::helper('pfg_mypos')->__('Amount'),
            'align'     => 'right',
            'index'     => 'amount',
            'type'      => 'currency',
            'currency_code' => 'currency',
        ));

        $this->addColumn('currency', array(
            'header'    => Mage::helper('pfg_mypos')->__('Currency'),
            'align'     => 'center',
            'index'     => 'currency',
            'width'     => '60px',
        ));

        $this->addColumn('status', array(
            'header'    => Mage::helper('pfg_mypos')->__('Status'),
            'align'     => 'center',
            'index'     => 'status',
            'type'      => 'options',
            'options'   => array(
                PFG_MyPOS_Model_Transaction::STATUS_PENDING => Mage::helper('pfg_mypos')->__('Pending'),
                PFG_MyPOS_Model_Transaction::STATUS_SUCCESS => Mage::helper('pfg_mypos')->__('Success'),
                PFG_MyPOS_Model_Transaction::STATUS_FAILED => Mage::helper('pfg_mypos')->__('Failed'),
                PFG_MyPOS_Model_Transaction::STATUS_CANCELLED => Mage::helper('pfg_mypos')->__('Cancelled'),
                PFG_MyPOS_Model_Transaction::STATUS_REFUNDED => Mage::helper('pfg_mypos')->__('Refunded'),
            ),
            'renderer'  => 'pfg_mypos/adminhtml_transaction_renderer_status',
        ));

        $this->addColumn('customer_email', array(
            'header'    => Mage::helper('pfg_mypos')->__('Customer Email'),
            'align'     => 'left',
            'index'     => 'customer_email',
        ));

        $this->addColumn('created_at', array(
            'header'    => Mage::helper('pfg_mypos')->__('Created At'),
            'align'     => 'center',
            'index'     => 'created_at',
            'type'      => 'datetime',
            'width'     => '160px',
        ));

        $this->addColumn('updated_at', array(
            'header'    => Mage::helper('pfg_mypos')->__('Updated At'),
            'align'     => 'center',
            'index'     => 'updated_at',
            'type'      => 'datetime',
            'width'     => '160px',
        ));

        $this->addColumn('action', array(
            'header'    => Mage::helper('pfg_mypos')->__('Action'),
            'width'     => '100px',
            'type'      => 'action',
            'getter'    => 'getId',
            'actions'   => array(
                array(
                    'caption'   => Mage::helper('pfg_mypos')->__('View'),
                    'url'       => array('base' => '*/*/viewTransaction'),
                    'field'     => 'id'
                )
            ),
            'filter'    => false,
            'sortable'  => false,
            'index'     => 'stores',
            'is_system' => true,
        ));

        return parent::_prepareColumns();
    }

    protected function _prepareMassaction()
    {
        $this->setMassactionIdField('transaction_id');
        $this->getMassactionBlock()->setFormFieldName('transaction_ids');

        $this->getMassactionBlock()->addItem('check_status', array(
            'label'    => Mage::helper('pfg_mypos')->__('Check Payment Status'),
            'url'      => $this->getUrl('*/*/massCheckStatus'),
            'confirm'  => Mage::helper('pfg_mypos')->__('Are you sure you want to check payment status for selected transactions?')
        ));

        return $this;
    }

    public function getRowUrl($row)
    {
        return $this->getUrl('*/*/viewTransaction', array('id' => $row->getId()));
    }
}
