<?php

class PFG_MyPOS_Block_Checkout_Form extends Mage_Payment_Block_Form
{
    protected function _construct()
    {
        parent::_construct();
        $this->setTemplate('pfg/mypos/checkout/form.phtml');
    }

    /**
     * Get payment method instructions
     *
     * @return string
     */
    public function getInstructions()
    {
        return $this->getMethod()->getInstructions();
    }

    /**
     * Get payment method title
     *
     * @return string
     */
    public function getMethodTitle()
    {
        return $this->getMethod()->getConfigData('title');
    }

    /**
     * Check if sandbox mode is enabled
     *
     * @return bool
     */
    public function isSandboxMode()
    {
        return $this->getMethod()->isSandboxMode();
    }

    /**
     * Get supported currencies
     *
     * @return array
     */
    public function getSupportedCurrencies()
    {
        return array('BGN', 'EUR', 'USD');
    }

    /**
     * Get current quote currency
     *
     * @return string
     */
    public function getQuoteCurrency()
    {
        $quote = Mage::getSingleton('checkout/session')->getQuote();
        return $quote->getQuoteCurrencyCode();
    }

    /**
     * Check if current currency is supported
     *
     * @return bool
     */
    public function isCurrencySupported()
    {
        return in_array($this->getQuoteCurrency(), $this->getSupportedCurrencies());
    }

    /**
     * Get MyPOS logo URL
     *
     * @return string
     */
    public function getMyPOSLogoUrl()
    {
        return $this->getSkinUrl('images/pfg/mypos/logo.png');
    }
}
