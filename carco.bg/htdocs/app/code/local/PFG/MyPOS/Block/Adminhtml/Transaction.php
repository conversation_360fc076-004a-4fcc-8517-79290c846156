<?php

class PFG_MyPOS_Block_Adminhtml_Transaction extends Mage_Adminhtml_Block_Widget_Grid_Container
{
    public function __construct()
    {
        $this->_controller = 'adminhtml_transaction';
        $this->_blockGroup = 'pfg_mypos';
        $this->_headerText = Mage::helper('pfg_mypos')->__('MyPOS Transactions');
        
        parent::__construct();
        
        // Remove add button as transactions are created automatically
        $this->_removeButton('add');
        
        // Add refresh button
        $this->_addButton('refresh', array(
            'label'     => Mage::helper('pfg_mypos')->__('Refresh'),
            'onclick'   => 'window.location.reload()',
            'class'     => 'refresh'
        ));
    }
}
