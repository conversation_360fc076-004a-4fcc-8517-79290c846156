<?php

class PFG_MyPOS_Block_Checkout_Info extends Mage_Payment_Block_Info
{
    protected function _construct()
    {
        parent::_construct();
        $this->setTemplate('pfg/mypos/checkout/info.phtml');
    }

    /**
     * Get payment method title
     *
     * @return string
     */
    public function getMethodTitle()
    {
        return $this->getMethod()->getConfigData('title');
    }

    /**
     * Get MyPOS transaction ID
     *
     * @return string|null
     */
    public function getMyPOSTransactionId()
    {
        return $this->getInfo()->getAdditionalInformation('mypos_transaction_id');
    }

    /**
     * Get order reference
     *
     * @return string|null
     */
    public function getOrderReference()
    {
        return $this->getInfo()->getAdditionalInformation('mypos_order_reference');
    }

    /**
     * Get payment status
     *
     * @return string|null
     */
    public function getPaymentStatus()
    {
        return $this->getInfo()->getAdditionalInformation('mypos_status');
    }

    /**
     * Get payment status message
     *
     * @return string|null
     */
    public function getPaymentStatusMessage()
    {
        return $this->getInfo()->getAdditionalInformation('mypos_status_message');
    }

    /**
     * Get transaction data for admin view
     *
     * @return array
     */
    public function getTransactionData()
    {
        $data = array();
        
        if ($this->getMyPOSTransactionId()) {
            $data['MyPOS Transaction ID'] = $this->getMyPOSTransactionId();
        }
        
        if ($this->getOrderReference()) {
            $data['Order Reference'] = $this->getOrderReference();
        }
        
        if ($this->getPaymentStatus()) {
            $data['Payment Status'] = $this->getPaymentStatus();
        }
        
        if ($this->getPaymentStatusMessage()) {
            $data['Status Message'] = $this->getPaymentStatusMessage();
        }
        
        return $data;
    }

    /**
     * Check if payment is successful
     *
     * @return bool
     */
    public function isPaymentSuccessful()
    {
        return $this->getPaymentStatus() === '00';
    }

    /**
     * Check if payment is pending
     *
     * @return bool
     */
    public function isPaymentPending()
    {
        return empty($this->getPaymentStatus()) || $this->getPaymentStatus() === 'pending';
    }

    /**
     * Get formatted payment amount
     *
     * @return string
     */
    public function getFormattedAmount()
    {
        $order = $this->getInfo()->getOrder();
        if ($order) {
            return $order->formatPrice($order->getGrandTotal());
        }
        
        return '';
    }

    /**
     * Get MyPOS logo URL
     *
     * @return string
     */
    public function getMyPOSLogoUrl()
    {
        return $this->getSkinUrl('images/pfg/mypos/logo-small.png');
    }
}
