<?php

/**
 * MyPOS Purchase SDK Wrapper
 * Basic implementation following MyPOS API v1.4 specifications
 */
class MyPOS_SDK_Purchase
{
    const SANDBOX_URL = 'https://mypos.com/vmp/checkout-test';
    const PRODUCTION_URL = 'https://mypos.com/vmp/checkout';
    
    private $config;
    private $data;
    
    public function __construct($config = array())
    {
        $this->config = $config;
        $this->data = array();
    }
    
    /**
     * Set store ID (SID)
     *
     * @param string $sid
     * @return $this
     */
    public function setStoreId($sid)
    {
        $this->data['SID'] = $sid;
        return $this;
    }
    
    /**
     * Set wallet number
     *
     * @param string $walletNumber
     * @return $this
     */
    public function setWalletNumber($walletNumber)
    {
        $this->data['WalletNumber'] = $walletNumber;
        return $this;
    }
    
    /**
     * Set amount in cents
     *
     * @param int $amount
     * @return $this
     */
    public function setAmount($amount)
    {
        $this->data['Amount'] = $amount;
        return $this;
    }
    
    /**
     * Set currency
     *
     * @param string $currency
     * @return $this
     */
    public function setCurrency($currency)
    {
        $this->data['Currency'] = $currency;
        return $this;
    }
    
    /**
     * Set order ID
     *
     * @param string $orderId
     * @return $this
     */
    public function setOrderId($orderId)
    {
        $this->data['OrderID'] = $orderId;
        return $this;
    }
    
    /**
     * Set language
     *
     * @param string $language
     * @return $this
     */
    public function setLanguage($language)
    {
        $this->data['IPCLanguage'] = $language;
        return $this;
    }
    
    /**
     * Set success URL
     *
     * @param string $url
     * @return $this
     */
    public function setUrlOk($url)
    {
        $this->data['URL_OK'] = $url;
        return $this;
    }
    
    /**
     * Set cancel URL
     *
     * @param string $url
     * @return $this
     */
    public function setUrlCancel($url)
    {
        $this->data['URL_Cancel'] = $url;
        return $this;
    }
    
    /**
     * Set notify URL
     *
     * @param string $url
     * @return $this
     */
    public function setUrlNotify($url)
    {
        $this->data['URL_Notify'] = $url;
        return $this;
    }
    
    /**
     * Set customer email
     *
     * @param string $email
     * @return $this
     */
    public function setCustomerEmail($email)
    {
        $this->data['customeremail'] = $email;
        return $this;
    }
    
    /**
     * Set customer first name
     *
     * @param string $firstName
     * @return $this
     */
    public function setCustomerFirstName($firstName)
    {
        $this->data['customerfirstname'] = $firstName;
        return $this;
    }
    
    /**
     * Set customer last name
     *
     * @param string $lastName
     * @return $this
     */
    public function setCustomerLastName($lastName)
    {
        $this->data['customerlastname'] = $lastName;
        return $this;
    }
    
    /**
     * Set customer phone
     *
     * @param string $phone
     * @return $this
     */
    public function setCustomerPhone($phone)
    {
        $this->data['customerphone'] = $phone;
        return $this;
    }

    /**
     * Set account settlement
     *
     * @param string $accountSettlement
     * @return $this
     */
    public function setAccountSettlement($accountSettlement = '')
    {
        $this->data['AccountSettlement'] = $accountSettlement;
        return $this;
    }

    /**
     * Set cart items
     *
     * @param array $items
     * @return $this
     */
    public function setCartItems($items)
    {
        $this->data['CartItems'] = is_array($items) ? json_encode($items) : $items;
        return $this;
    }
    
    /**
     * Set note
     *
     * @param string $note
     * @return $this
     */
    public function setNote($note)
    {
        $this->data['Note'] = $note;
        return $this;
    }
    
    /**
     * Set output format
     *
     * @param string $format
     * @return $this
     */
    public function setOutputFormat($format = 'json')
    {
        $this->data['OutputFormat'] = $format;
        return $this;
    }
    
    /**
     * Generate signature
     *
     * @param string $privateKeyPath
     * @param string $privateKeyPassword
     * @return string
     * @throws Exception
     */
    public function generateSignature($privateKeyPath, $privateKeyPassword = null)
    {
        if (!file_exists($privateKeyPath)) {
            // Enhanced error message with debugging info
            $debugInfo = array(
                'Expected path' => $privateKeyPath,
                'Current working directory' => getcwd(),
                'File exists check' => file_exists($privateKeyPath) ? 'YES' : 'NO',
                'Is readable' => is_readable($privateKeyPath) ? 'YES' : 'NO'
            );

            throw new Exception('Private key file does not exist. Debug: ' . json_encode($debugInfo));
        }
        
        $privateKey = file_get_contents($privateKeyPath);
        
        if ($privateKeyPassword) {
            $privateKey = openssl_pkey_get_private($privateKey, $privateKeyPassword);
        } else {
            $privateKey = openssl_pkey_get_private($privateKey);
        }
        
        if (!$privateKey) {
            throw new Exception('Could not load private key');
        }
        
        $dataString = $this->buildSignatureString();
        $signature = '';
        
        // Use SHA-256 algorithm as per MyPOS documentation
        if (!openssl_sign($dataString, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {
            throw new Exception('Could not generate signature');
        }
        
        return base64_encode($signature);
    }
    
    /**
     * Build signature string
     *
     * @return string
     */
    private function buildSignatureString()
    {
        $data = $this->data;

        // Remove fields that should not be included in signature
        unset($data['Signature']);
        // Keep keyindex and CartItems in signature - they might be required

        // Sort by key (case-sensitive) - this is critical for MyPOS signature validation
        ksort($data);

        // According to MyPOS documentation: concatenate all VALUES with dashes, then Base64 encode
        $values = array();
        foreach ($data as $key => $value) {
            // Handle different value types for signature
            if (is_array($value)) {
                $value = json_encode($value, JSON_UNESCAPED_UNICODE);
            }
            $values[] = $value;
        }

        // Concatenate values with dashes as per MyPOS documentation
        $concatenatedValues = implode('-', $values);

        // Base64 encode the concatenated string as per MyPOS documentation
        $finalString = base64_encode($concatenatedValues);

        // Log signature data for debugging
        Mage::log('MyPOS Signature Data: ' . print_r($data, true), Zend_Log::INFO, 'mypos_debug.log');
        Mage::log('MyPOS Signature String: ' . $finalString, Zend_Log::INFO, 'mypos_debug.log');

        return $finalString;
    }
    
    /**
     * Get checkout URL
     *
     * @param bool $sandbox
     * @return string
     */
    public function getCheckoutUrl($sandbox = false)
    {
        $url = $sandbox ? self::SANDBOX_URL : self::PRODUCTION_URL;
        Mage::log('MyPOS SDK checkout URL: ' . $url . ' (sandbox: ' . ($sandbox ? 'YES' : 'NO') . ')', Zend_Log::INFO, 'mypos_debug.log');
        return $url;
    }
    
    /**
     * Get form data for POST
     *
     * @param string $privateKeyPath
     * @param string $privateKeyPassword
     * @param bool $sandbox
     * @return array
     * @throws Exception
     */
    public function getFormData($privateKeyPath, $privateKeyPassword = null, $sandbox = false, $keyIndex = null)
    {
        // Set required fields first
        $this->data['IPCmethod'] = 'IPCPurchase';
        $this->data['IPCVersion'] = '1.4';

        // Add AccountSettlement parameter (empty as per MyPOS support recommendation)
        $this->data['AccountSettlement'] = '';

        // Add key index if provided (must be set before signature generation)
        if ($keyIndex !== null) {
            $this->data['keyindex'] = $keyIndex;
        }

        // Log data before signature generation
        Mage::log('MyPOS Data before signature: ' . print_r($this->data, true), Zend_Log::INFO, 'mypos_debug.log');

        // Generate signature (includes all data including keyindex)
        $this->data['Signature'] = $this->generateSignature($privateKeyPath, $privateKeyPassword);

        return array(
            'url' => $this->getCheckoutUrl($sandbox),
            'data' => $this->data
        );
    }
    
    /**
     * Get data array
     *
     * @return array
     */
    public function getData()
    {
        return $this->data;
    }
}
