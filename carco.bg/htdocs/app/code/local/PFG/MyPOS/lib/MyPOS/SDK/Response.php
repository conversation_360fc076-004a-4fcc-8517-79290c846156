<?php

/**
 * MyPOS Response Handler
 * Handles responses from MyPOS payment gateway
 */
class MyPOS_SDK_Response
{
    private $data;
    private $isValid = false;
    
    public function __construct($responseData = array())
    {
        $this->data = $responseData;
    }
    
    /**
     * Create response from POST data
     *
     * @param array $postData
     * @return MyPOS_SDK_Response
     */
    public static function fromPost($postData = null)
    {
        if ($postData === null) {
            $postData = $_POST;
        }
        
        return new self($postData);
    }
    
    /**
     * Validate response signature
     *
     * @param string $publicKeyPath
     * @return bool
     */
    public function validateSignature($publicKeyPath)
    {
        if (!file_exists($publicKeyPath)) {
            return false;
        }
        
        if (!isset($this->data['Signature'])) {
            return false;
        }
        
        try {
            $publicKey = file_get_contents($publicKeyPath);
            $signature = $this->data['Signature'];
            $dataString = $this->buildSignatureString();
            
            $this->isValid = openssl_verify($dataString, base64_decode($signature), $publicKey, OPENSSL_ALGO_SHA1) === 1;
            
        } catch (Exception $e) {
            $this->isValid = false;
        }
        
        return $this->isValid;
    }
    
    /**
     * Build signature string for verification
     *
     * @return string
     */
    private function buildSignatureString()
    {
        $data = $this->data;
        unset($data['Signature']);
        
        ksort($data);
        
        $signatureString = '';
        foreach ($data as $key => $value) {
            $signatureString .= $key . '=' . $value . '&';
        }
        
        return rtrim($signatureString, '&');
    }
    
    /**
     * Check if response is valid
     *
     * @return bool
     */
    public function isValid()
    {
        return $this->isValid;
    }
    
    /**
     * Check if payment was successful
     *
     * @return bool
     */
    public function isSuccessful()
    {
        return isset($this->data['Status']) && $this->data['Status'] === '00';
    }
    
    /**
     * Check if payment was cancelled
     *
     * @return bool
     */
    public function isCancelled()
    {
        return isset($this->data['Status']) && in_array($this->data['Status'], array('01', '02', '03'));
    }
    
    /**
     * Check if payment failed
     *
     * @return bool
     */
    public function isFailed()
    {
        return isset($this->data['Status']) && !in_array($this->data['Status'], array('00', '01', '02', '03'));
    }
    
    /**
     * Get status code
     *
     * @return string|null
     */
    public function getStatus()
    {
        return isset($this->data['Status']) ? $this->data['Status'] : null;
    }
    
    /**
     * Get status message
     *
     * @return string|null
     */
    public function getStatusMessage()
    {
        return isset($this->data['StatusMsg']) ? $this->data['StatusMsg'] : null;
    }
    
    /**
     * Get transaction reference
     *
     * @return string|null
     */
    public function getTransactionReference()
    {
        return isset($this->data['IPC_Trnref']) ? $this->data['IPC_Trnref'] : null;
    }
    
    /**
     * Get order ID
     *
     * @return string|null
     */
    public function getOrderId()
    {
        return isset($this->data['OrderID']) ? $this->data['OrderID'] : null;
    }
    
    /**
     * Get amount
     *
     * @return float|null
     */
    public function getAmount()
    {
        return isset($this->data['Amount']) ? (float)($this->data['Amount'] / 100) : null;
    }
    
    /**
     * Get currency
     *
     * @return string|null
     */
    public function getCurrency()
    {
        return isset($this->data['Currency']) ? $this->data['Currency'] : null;
    }
    
    /**
     * Get all response data
     *
     * @return array
     */
    public function getData()
    {
        return $this->data;
    }
    
    /**
     * Get specific field from response
     *
     * @param string $field
     * @param mixed $default
     * @return mixed
     */
    public function get($field, $default = null)
    {
        return isset($this->data[$field]) ? $this->data[$field] : $default;
    }
    
    /**
     * Get error message for failed transactions
     *
     * @return string
     */
    public function getErrorMessage()
    {
        if ($this->isSuccessful()) {
            return '';
        }
        
        $statusMsg = $this->getStatusMessage();
        if ($statusMsg) {
            return $statusMsg;
        }
        
        // Default error messages based on status codes
        $status = $this->getStatus();
        $errorMessages = array(
            '01' => 'Transaction cancelled by user',
            '02' => 'Transaction timeout',
            '03' => 'Transaction declined',
            '04' => 'Invalid card number',
            '05' => 'Insufficient funds',
            '06' => 'Card expired',
            '07' => 'Invalid CVV',
            '08' => 'Card blocked',
            '09' => 'Transaction limit exceeded',
            '10' => 'System error'
        );
        
        return isset($errorMessages[$status]) ? $errorMessages[$status] : 'Unknown error occurred';
    }
}
