<?php

/**
 * MyPOS SDK Autoloader
 */
class MyPOS_Autoloader
{
    private static $registered = false;
    
    /**
     * Register autoloader
     */
    public static function register()
    {
        if (self::$registered) {
            return;
        }
        
        spl_autoload_register(array(__CLASS__, 'autoload'));
        self::$registered = true;
    }
    
    /**
     * Autoload classes
     *
     * @param string $className
     */
    public static function autoload($className)
    {
        if (strpos($className, 'MyPOS_') !== 0) {
            return;
        }
        
        $classPath = str_replace('_', DIRECTORY_SEPARATOR, $className);
        $filePath = dirname(__FILE__) . DIRECTORY_SEPARATOR . $classPath . '.php';
        
        if (file_exists($filePath)) {
            require_once $filePath;
        }
    }
}

// Register the autoloader
MyPOS_Autoloader::register();
