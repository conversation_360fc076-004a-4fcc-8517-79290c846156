<?xml version="1.0"?>
<config>
    <modules>
        <PFG_MyPOS>
            <version>0.1.0</version>
        </PFG_MyPOS>
    </modules>
    <global>
        <models>
            <pfg_mypos>
                <class>PFG_MyPOS_Model</class>
                <resourceModel>pfg_mypos_resource</resourceModel>
            </pfg_mypos>
            <pfg_mypos_resource>
                <class>PFG_MyPOS_Model_Resource</class>
                <entities>
                    <transactions>
                        <table>pfg_mypos_transactions</table>
                    </transactions>
                    <pfg_mypos_transactions>
                        <table>pfg_mypos_transactions</table>
                    </pfg_mypos_transactions>
                </entities>
            </pfg_mypos_resource>
        </models>
        <resources>
            <pfg_mypos_setup>
                <setup>
                    <module>PFG_MyPOS</module>
                </setup>
            </pfg_mypos_setup>
        </resources>
        <blocks>
            <pfg_mypos>
                <class>PFG_MyPOS_Block</class>
            </pfg_mypos>
        </blocks>
        <helpers>
            <pfg_mypos>
                <class>PFG_MyPOS_Helper</class>
            </pfg_mypos>
        </helpers>
    </global>

    <frontend>
        <routers>
            <pfg_mypos>
                <use>standard</use>
                <args>
                    <module>PFG_MyPOS</module>
                    <frontName>pfg_mypos</frontName>
                </args>
            </pfg_mypos>
        </routers>
        <layout>
            <updates>
                <pfg_mypos>
                    <file>pfg/mypos/checkout.xml</file>
                </pfg_mypos>
            </updates>
        </layout>
        <events>
            <sales_order_place_after>
                <observers>
                    <pfg_mypos_change_new_order_status>
                        <type>singleton</type>
                        <class>pfg_mypos/observer</class>
                        <method>myposChangeOrderStatus</method>
                    </pfg_mypos_change_new_order_status>
                </observers>
            </sales_order_place_after>
        </events>
    </frontend>

    <adminhtml>
        <events>
            <core_block_abstract_prepare_layout_after>
                <observers>
                    <pfg_mypos_add_check_payment_status_button>
                        <type>model</type>
                        <class>pfg_mypos/adminhtml_observer</class>
                        <method>addCheckPaymentStatusButton</method>
                    </pfg_mypos_add_check_payment_status_button>
                </observers>
            </core_block_abstract_prepare_layout_after>
        </events>
    </adminhtml>

    <admin>
        <routers>
            <adminhtml>
                <args>
                    <modules>
                        <PFG_MyPOS before="Mage_Adminhtml">PFG_MyPOS_Adminhtml</PFG_MyPOS>
                    </modules>
                </args>
            </adminhtml>
        </routers>
    </admin>

    <default>
        <payment>
            <pfg_mypos>
                <active>0</active>
                <model>pfg_mypos/payment_mypos</model>
                <title>MyPOS Credit Card Payment</title>
                <new_order_status>pending</new_order_status>
                <order_status_after_payment>processing</order_status_after_payment>
                <sandbox_mode>1</sandbox_mode>
                <sort_order>110</sort_order>
                <currency>BGN</currency>
                <language>BG</language>
            </pfg_mypos>
        </payment>
        <pfg_mypos>
            <advanced>
                <force_log>0</force_log>
                <log_file_name><![CDATA[pfg_mypos.log]]></log_file_name>
                <exceptions_file_name><![CDATA[pfg_mypos_exceptions.log]]></exceptions_file_name>
            </advanced>
        </pfg_mypos>
    </default>

    <crontab>
        <jobs>
            <pfg_mypos_check_payments>
                <schedule>
                    <cron_expr>*/10 * * * *</cron_expr>
                </schedule>
                <run>
                    <model>pfg_mypos/cron::checkOrdersPaymentStatuses</model>
                </run>
            </pfg_mypos_check_payments>
        </jobs>
    </crontab>
</config>
