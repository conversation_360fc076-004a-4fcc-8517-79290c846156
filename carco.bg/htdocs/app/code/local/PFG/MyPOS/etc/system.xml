<?xml version="1.0"?>
<config>
    <sections>
        <payment>
            <groups>
                <pfg_mypos translate="label" module="pfg_mypos">
                    <label>MyPOS Payment Gateway</label>
                    <sort_order>110</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>0</show_in_store>
                    <fields>
                        <active translate="label">
                            <label>Enabled</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </active>
                        <title translate="label">
                            <label>Title</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <active>1</active>
                            </depends>
                        </title>
                        <instructions translate="label">
                            <label>Instructions</label>
                            <comment><![CDATA[Visible to the user in the frontend]]></comment>
                            <frontend_type>textarea</frontend_type>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <active>1</active>
                            </depends>
                        </instructions>
                        <description translate="label">
                            <label>Payment Method Description</label>
                            <comment><![CDATA[Visible for the client when redirected to MyPOS checkout page]]></comment>
                            <frontend_type>textarea</frontend_type>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <active>1</active>
                            </depends>
                            <validate>required-entry</validate>
                        </description>
                        <new_order_status translate="label">
                            <label>New Order Status</label>
                            <frontend_type>select</frontend_type>
                            <source_model>pfg_mypos/system_config_source_order_status_all</source_model>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <active>1</active>
                            </depends>
                        </new_order_status>
                        <order_status_after_payment translate="label">
                            <label>Order Status After Payment</label>
                            <frontend_type>select</frontend_type>
                            <source_model>pfg_mypos/system_config_source_order_status_all</source_model>
                            <sort_order>60</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <active>1</active>
                            </depends>
                        </order_status_after_payment>
                        <payment_failed_cms_page translate="label">
                            <label>Payment Failed CMS Page</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_cms_page</source_model>
                            <sort_order>65</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <active>1</active>
                            </depends>
                        </payment_failed_cms_page>
                        <store_id translate="label comment">
                            <label>Store ID (SID)</label>
                            <comment><![CDATA[Provided by MyPOS]]></comment>
                            <frontend_type>text</frontend_type>
                            <sort_order>70</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <active>1</active>
                            </depends>
                            <validate>required-entry</validate>
                        </store_id>
                        <client_number translate="label comment">
                            <label>Client Number</label>
                            <comment><![CDATA[Your MyPOS client number (from Configuration Pack)]]></comment>
                            <frontend_type>text</frontend_type>
                            <sort_order>80</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <active>1</active>
                            </depends>
                            <validate>required-entry</validate>
                        </client_number>
                        <currency translate="label comment">
                            <label>Currency</label>
                            <comment><![CDATA[BGN, EUR or USD. Should be agreed upon with MyPOS]]></comment>
                            <frontend_type>select</frontend_type>
                            <source_model>pfg_mypos/system_config_source_currency</source_model>
                            <sort_order>90</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <active>1</active>
                            </depends>
                        </currency>
                        <language translate="label comment">
                            <label><![CDATA[Language used in MyPOS Web Interface]]></label>
                            <frontend_type>select</frontend_type>
                            <source_model>pfg_mypos/system_config_source_language</source_model>
                            <sort_order>100</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment>BG, EN</comment>
                            <depends>
                                <active>1</active>
                            </depends>
                        </language>
                        <private_key_path translate="label comment">
                            <label>Relative Path to the Private Key File</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>110</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment><![CDATA[File path relative to the Magento var/ folder.]]></comment>
                            <depends>
                                <active>1</active>
                            </depends>
                            <validate>required-entry</validate>
                        </private_key_path>
                        <private_key_password translate="label comment">
                            <label>Private Key Password</label>
                            <frontend_type>password</frontend_type>
                            <sort_order>120</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment><![CDATA[Password for your private key file]]></comment>
                            <depends>
                                <active>1</active>
                            </depends>
                        </private_key_password>
                        <public_key_path translate="label">
                            <label>Relative Path to the Public Key File</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>130</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment><![CDATA[File path relative to the Magento var/ folder.]]></comment>
                            <depends>
                                <active>1</active>
                            </depends>
                            <validate>required-entry</validate>
                        </public_key_path>
                        <sandbox_mode translate="label">
                            <label><![CDATA[Sandbox Mode]]></label>
                            <comment><![CDATA[Whether to use the development or production environment]]></comment>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>140</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <active>1</active>
                            </depends>
                        </sandbox_mode>
                        <sort_order>
                            <label>Payment method sort order</label>
                            <frontend_type>text</frontend_type>
                            <comment><![CDATA[For the payment methods section on the frontend]]></comment>
                            <sort_order>150</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <active>1</active>
                            </depends>
                        </sort_order>
                    </fields>
                </pfg_mypos>
            </groups>
        </payment>
    </sections>
</config>
