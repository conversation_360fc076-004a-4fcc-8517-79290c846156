# MyPOS Configuration Guide

## 🔑 Your MyPOS Credentials

Based on your MyPOS account data:
- **Client Number**: `HSdDQ8dSl9MVzoMbGxgRVLyt`
- **Client Code**: `DfqEPIicwSZPl7hQw4ONay6SlE6BUtAjKgnBu0DVJIdwsrZB`
- **Date**: 07.06.2025

## 📋 Step-by-Step Configuration

### Step 1: Get Your RSA Keys from MyPOS

**According to MyPOS documentation, you need:**
1. RSA Private Key (2048-bit, PEM format) - for signing requests
2. RSA Public Key (2048-bit, PEM format) - for MyPOS to verify your requests
3. MyPOS Public Certificate - for verifying MyPOS responses

**Method 1: Generate Keys in MyPOS Account (Recommended)**
1. Log into your MyPOS merchant account
2. Go to **Integration** section
3. Select **Custom Integration Method**
4. Click **"Generate Pair Keys"**
5. Copy the generated private and public keys
6. Download the MyPOS public certificate

**Method 2: Use Configuration Pack (Easiest)**
1. In your MyPOS account integration section
2. Look for **"Configuration Pack"** option
3. This includes: Key pair + Key Index + Store ID + Client Number
4. Use this single string for easy setup

**Your Current Credentials:**
- Store ID: `HSdDQ8dSl9MVzoMbGxgRVLyt`
- Client Number: `DfqEPIicwSZPl7hQw4ONay6SlE6BUtAjKgnBu0DVJIdwsrZB`

### Step 2: Prepare Key Files

Once you get the keys from MyPOS:

1. **Create directory:**
   ```bash
   mkdir -p /home/<USER>/git/carco-project/carco.bg/htdocs/var/mypos/
   ```

2. **Upload your key files to:**
   - `/home/<USER>/git/carco-project/carco.bg/htdocs/var/mypos/private_key.pem`
   - `/home/<USER>/git/carco-project/carco.bg/htdocs/var/mypos/public_key.pem`

3. **Set permissions:**
   ```bash
   chmod 600 /home/<USER>/git/carco-project/carco.bg/htdocs/var/mypos/private_key.pem
   chmod 644 /home/<USER>/git/carco-project/carco.bg/htdocs/var/mypos/public_key.pem
   ```

### Step 3: Configure in Magento Admin

1. **Access Magento Admin:**
   - Go to your Magento admin panel at `/ecomin`
   - Navigate to: **System > Configuration > Payment Methods**

2. **Find MyPOS Payment Gateway section and configure:**

   **Basic Settings:**
   - ✅ **Enabled**: Yes
   - ✅ **Title**: MyPOS Credit Card Payment
   - ✅ **Instructions**: You will be redirected to MyPOS secure payment page
   - ✅ **Description**: Payment via MyPOS gateway

   **MyPOS API Settings:**
   - ✅ **Store ID (SID)**: `HSdDQ8dSl9MVzoMbGxgRVLyt` *(verify with MyPOS)*
   - ✅ **Wallet Number**: `DfqEPIicwSZPl7hQw4ONay6SlE6BUtAjKgnBu0DVJIdwsrZB` *(verify with MyPOS)*
   - ✅ **Currency**: BGN *(or as confirmed by MyPOS)*
   - ✅ **Language**: BG

   **Security Settings:**
   - ✅ **Private Key Path**: `mypos/private_key.pem`
   - ✅ **Private Key Password**: *(leave empty unless MyPOS provides one)*
   - ✅ **Public Key Path**: `mypos/public_key.pem`
   - ✅ **Sandbox Mode**: Yes *(for testing first)*

   **Order Status Settings:**
   - ✅ **New Order Status**: pending
   - ✅ **Order Status After Payment**: processing

   **Advanced Settings:**
   - ✅ **Sort Order**: 110

3. **Save Configuration**

### Step 4: Test the Integration

1. **Clear Magento Cache:**
   ```bash
   rm -rf /home/<USER>/git/carco-project/carco.bg/htdocs/var/cache/*
   ```

2. **Test Frontend:**
   - Go to your website checkout
   - Verify "MyPOS плащане" appears as payment option
   - Test the payment flow in sandbox mode

3. **Check Admin:**
   - Go to **Sales > MyPOS Transactions** (should appear in menu)
   - Verify you can access the transaction management

### Step 5: Go Live

Once testing is successful:
1. Switch **Sandbox Mode** to **No**
2. Update with production credentials if different
3. Test with small real transaction

## 🔍 Credential Mapping Analysis

Based on typical MyPOS patterns, I recommend:
- **Store ID (SID)**: `HSdDQ8dSl9MVzoMbGxgRVLyt` (shorter, 24 chars)
- **Wallet Number**: `DfqEPIicwSZPl7hQw4ONay6SlE6BUtAjKgnBu0DVJIdwsrZB` (longer, 48 chars)

**⚠️ Important**: Please confirm this mapping with MyPOS support!

## 🚨 Troubleshooting

**If payment method doesn't appear:**
- Check module is enabled: System > Configuration > Advanced > Advanced
- Clear cache: `rm -rf var/cache/*`
- Check error logs: `var/log/system.log`

**If signature errors occur:**
- Verify key file paths and permissions
- Check key file format (must be valid PEM)
- Confirm Store ID and Wallet Number are correct

**If redirects fail:**
- Ensure SSL is configured
- Check callback URLs are accessible
- Verify MyPOS account is active

## 📞 Support Contacts

**MyPOS Support:**
- Email: <EMAIL>
- Website: https://mypos.com/

**For Integration Issues:**
- Check logs in: `var/log/pfg_mypos.log`
- Run validation: `php app/code/local/PFG/MyPOS/Test/validate_installation.php`

## ✅ Configuration Checklist

- [ ] Contact MyPOS for private/public keys
- [ ] Upload key files to `var/mypos/` directory  
- [ ] Set correct file permissions
- [ ] Configure payment method in Magento admin
- [ ] Clear Magento cache
- [ ] Test in sandbox mode
- [ ] Verify React frontend shows MyPOS option
- [ ] Test complete payment flow
- [ ] Switch to production mode
- [ ] Test with real transaction

Once you complete these steps, your MyPOS payment method will be fully operational across your entire stack!
