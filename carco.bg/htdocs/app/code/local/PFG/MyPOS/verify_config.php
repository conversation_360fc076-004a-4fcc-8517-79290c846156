<?php
/**
 * Verify MyPOS Configuration
 */

// Simple verification without Magento dependencies
echo "MyPOS Configuration Verification\n";
echo "================================\n\n";

// Check database configuration
try {
    $pdo = new PDO('mysql:host=carco-db;dbname=carco', 'root', 'root');
    
    echo "✅ Database connection successful\n\n";
    
    // Check MyPOS configuration
    $stmt = $pdo->prepare("SELECT path, value FROM core_config_data WHERE path LIKE 'payment/pfg_mypos/%' ORDER BY path");
    $stmt->execute();
    $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "📋 MyPOS Configuration in Database:\n";
    echo "-----------------------------------\n";
    
    $requiredFields = array(
        'payment/pfg_mypos/active' => 'Active',
        'payment/pfg_mypos/store_id' => 'Store ID',
        'payment/pfg_mypos/client_number' => 'Client Number',
        'payment/pfg_mypos/private_key_path' => 'Private Key Path',
        'payment/pfg_mypos/public_key_path' => 'Public Key Path'
    );
    
    $foundFields = array();
    
    foreach ($configs as $config) {
        $path = $config['path'];
        $value = $config['value'];
        
        if (isset($requiredFields[$path])) {
            $foundFields[$path] = $value;
            echo "✅ {$requiredFields[$path]}: {$value}\n";
        } else {
            echo "ℹ️  {$path}: {$value}\n";
        }
    }
    
    echo "\n🔍 Checking Required Fields:\n";
    echo "----------------------------\n";
    
    $allRequired = true;
    foreach ($requiredFields as $path => $label) {
        if (isset($foundFields[$path]) && !empty($foundFields[$path])) {
            echo "✅ {$label}: Present\n";
        } else {
            echo "❌ {$label}: Missing\n";
            $allRequired = false;
        }
    }
    
    echo "\n📁 Checking Key Files:\n";
    echo "----------------------\n";
    
    $privateKeyPath = '/home/<USER>/git/carco-project/carco.bg/htdocs/app/var/mypos/private_key.pem';
    $publicKeyPath = '/home/<USER>/git/carco-project/carco.bg/htdocs/app/var/mypos/public_key.pem';
    
    if (file_exists($privateKeyPath)) {
        $perms = substr(sprintf('%o', fileperms($privateKeyPath)), -4);
        echo "✅ Private Key: Exists (permissions: {$perms})\n";
    } else {
        echo "❌ Private Key: Missing\n";
        $allRequired = false;
    }
    
    if (file_exists($publicKeyPath)) {
        $perms = substr(sprintf('%o', fileperms($publicKeyPath)), -4);
        echo "✅ Public Key: Exists (permissions: {$perms})\n";
    } else {
        echo "❌ Public Key: Missing\n";
        $allRequired = false;
    }
    
    echo "\n🎯 Overall Status:\n";
    echo "-----------------\n";
    
    if ($allRequired) {
        echo "🎉 SUCCESS: MyPOS is fully configured and ready!\n\n";
        echo "📝 Next Steps:\n";
        echo "1. Go to Magento Admin: System > Configuration > Payment Methods\n";
        echo "2. Find 'MyPOS Payment Gateway' section\n";
        echo "3. Verify all fields are populated\n";
        echo "4. Save configuration\n";
        echo "5. Test checkout on frontend\n";
    } else {
        echo "❌ INCOMPLETE: Some required configuration is missing\n";
    }
    
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
}
