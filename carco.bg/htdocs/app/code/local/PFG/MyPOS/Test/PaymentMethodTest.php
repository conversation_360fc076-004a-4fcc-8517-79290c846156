<?php

/**
 * MyPOS Payment Method Test
 */
class PFG_MyPOS_Test_PaymentMethodTest extends PHPUnit_Framework_TestCase
{
    protected $paymentMethod;
    
    protected function setUp()
    {
        $this->paymentMethod = Mage::getModel('pfg_mypos/payment_mypos');
    }
    
    public function testPaymentMethodCode()
    {
        $this->assertEquals('pfg_mypos', $this->paymentMethod->getCode());
    }
    
    public function testPaymentMethodCapabilities()
    {
        $this->assertTrue($this->paymentMethod->canCapture());
        $this->assertTrue($this->paymentMethod->canRefund());
        $this->assertTrue($this->paymentMethod->canRefundInvoicePartial());
        $this->assertFalse($this->paymentMethod->canVoid());
        $this->assertFalse($this->paymentMethod->canUseInternal());
        $this->assertTrue($this->paymentMethod->canUseCheckout());
        $this->assertFalse($this->paymentMethod->canUseForMultishipping());
    }
    
    public function testConfigurationValidation()
    {
        // Mock configuration
        $this->paymentMethod->setData('store_id', 'test_store_id');
        $this->paymentMethod->setData('wallet_number', 'test_wallet');
        $this->paymentMethod->setData('private_key_path', 'test_private_key.pem');
        $this->paymentMethod->setData('public_key_path', 'test_public_key.pem');
        
        // Test configuration getters
        $this->assertEquals('test_store_id', $this->paymentMethod->getStoreId());
        $this->assertEquals('test_wallet', $this->paymentMethod->getWalletNumber());
        $this->assertEquals('BGN', $this->paymentMethod->getCurrency()); // Default
        $this->assertEquals('BG', $this->paymentMethod->getLanguage()); // Default
    }
    
    public function testOrderPlaceRedirectUrl()
    {
        $redirectUrl = $this->paymentMethod->getOrderPlaceRedirectUrl();
        $this->assertContains('pfg_mypos/request/redirect', $redirectUrl);
    }
    
    public function testSandboxMode()
    {
        // Test default sandbox mode
        $this->paymentMethod->setData('sandbox_mode', 1);
        $this->assertTrue($this->paymentMethod->isSandboxMode());
        
        $this->paymentMethod->setData('sandbox_mode', 0);
        $this->assertFalse($this->paymentMethod->isSandboxMode());
    }
}
