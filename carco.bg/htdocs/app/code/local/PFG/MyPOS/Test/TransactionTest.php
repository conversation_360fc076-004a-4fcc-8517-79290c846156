<?php

/**
 * MyPOS Transaction Model Test
 */
class PFG_MyPOS_Test_TransactionTest extends PHPUnit_Framework_TestCase
{
    protected $transaction;
    
    protected function setUp()
    {
        $this->transaction = Mage::getModel('pfg_mypos/transaction');
    }
    
    public function testTransactionStatuses()
    {
        // Test pending status
        $this->transaction->setStatus(PFG_MyPOS_Model_Transaction::STATUS_PENDING);
        $this->assertTrue($this->transaction->isPending());
        $this->assertFalse($this->transaction->isSuccessful());
        $this->assertFalse($this->transaction->isFailed());
        $this->assertFalse($this->transaction->isCancelled());
        $this->assertFalse($this->transaction->isRefunded());
        
        // Test successful status
        $this->transaction->setStatus(PFG_MyPOS_Model_Transaction::STATUS_SUCCESS);
        $this->assertFalse($this->transaction->isPending());
        $this->assertTrue($this->transaction->isSuccessful());
        $this->assertFalse($this->transaction->isFailed());
        $this->assertFalse($this->transaction->isCancelled());
        $this->assertFalse($this->transaction->isRefunded());
        
        // Test failed status
        $this->transaction->setStatus(PFG_MyPOS_Model_Transaction::STATUS_FAILED);
        $this->assertFalse($this->transaction->isPending());
        $this->assertFalse($this->transaction->isSuccessful());
        $this->assertTrue($this->transaction->isFailed());
        $this->assertFalse($this->transaction->isCancelled());
        $this->assertFalse($this->transaction->isRefunded());
    }
    
    public function testMarkAsSuccessful()
    {
        $transactionId = 'test_transaction_123';
        $responseData = array('Status' => '00', 'StatusMsg' => 'Success');
        
        $this->transaction->markAsSuccessful($transactionId, $responseData);
        
        $this->assertEquals(PFG_MyPOS_Model_Transaction::STATUS_SUCCESS, $this->transaction->getStatus());
        $this->assertEquals($transactionId, $this->transaction->getMyposTransactionId());
        $this->assertEquals(json_encode($responseData), $this->transaction->getResponseData());
    }
    
    public function testMarkAsFailed()
    {
        $errorMessage = 'Payment failed';
        $responseData = array('Status' => '05', 'StatusMsg' => $errorMessage);
        
        $this->transaction->markAsFailed($errorMessage, $responseData);
        
        $this->assertEquals(PFG_MyPOS_Model_Transaction::STATUS_FAILED, $this->transaction->getStatus());
        $this->assertEquals($errorMessage, $this->transaction->getErrorMessage());
        $this->assertEquals(json_encode($responseData), $this->transaction->getResponseData());
    }
    
    public function testMarkAsCancelled()
    {
        $responseData = array('Status' => '01', 'StatusMsg' => 'Cancelled by user');
        
        $this->transaction->markAsCancelled($responseData);
        
        $this->assertEquals(PFG_MyPOS_Model_Transaction::STATUS_CANCELLED, $this->transaction->getStatus());
        $this->assertEquals(json_encode($responseData), $this->transaction->getResponseData());
    }
    
    public function testDecodedData()
    {
        $requestData = array('Amount' => 1000, 'Currency' => 'BGN');
        $responseData = array('Status' => '00', 'IPC_Trnref' => '123456');
        
        $this->transaction->setRequestData(json_encode($requestData));
        $this->transaction->setResponseData(json_encode($responseData));
        
        $this->assertEquals($requestData, $this->transaction->getDecodedRequestData());
        $this->assertEquals($responseData, $this->transaction->getDecodedResponseData());
    }
}
