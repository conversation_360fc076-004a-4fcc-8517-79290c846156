<?php

/**
 * MyPOS SDK Test
 */
class PFG_MyPOS_Test_SDKTest extends PHPUnit_Framework_TestCase
{
    protected $purchase;
    protected $response;
    
    protected function setUp()
    {
        // Load SDK
        require_once Mage::getBaseDir('code') . '/local/PFG/MyPOS/lib/MyPOS/autoload.php';
        
        $this->purchase = new MyPOS_SDK_Purchase();
        $this->response = new MyPOS_SDK_Response();
    }
    
    public function testPurchaseDataSetting()
    {
        $this->purchase->setStoreId('test_store')
                      ->setWalletNumber('test_wallet')
                      ->setAmount(1000)
                      ->setCurrency('BGN')
                      ->setOrderId('test_order_123')
                      ->setLanguage('BG');
        
        $data = $this->purchase->getData();
        
        $this->assertEquals('test_store', $data['SID']);
        $this->assertEquals('test_wallet', $data['WalletNumber']);
        $this->assertEquals(1000, $data['Amount']);
        $this->assertEquals('BGN', $data['Currency']);
        $this->assertEquals('test_order_123', $data['OrderID']);
        $this->assertEquals('BG', $data['IPCLanguage']);
    }
    
    public function testPurchaseUrls()
    {
        $this->purchase->setUrlOk('http://example.com/success')
                      ->setUrlCancel('http://example.com/cancel')
                      ->setUrlNotify('http://example.com/notify');
        
        $data = $this->purchase->getData();
        
        $this->assertEquals('http://example.com/success', $data['URL_OK']);
        $this->assertEquals('http://example.com/cancel', $data['URL_Cancel']);
        $this->assertEquals('http://example.com/notify', $data['URL_Notify']);
    }
    
    public function testPurchaseCustomerData()
    {
        $this->purchase->setCustomerEmail('<EMAIL>')
                      ->setCustomerFirstName('John')
                      ->setCustomerLastName('Doe')
                      ->setCustomerPhone('+359888123456');
        
        $data = $this->purchase->getData();
        
        $this->assertEquals('<EMAIL>', $data['customeremail']);
        $this->assertEquals('John', $data['customerfirstname']);
        $this->assertEquals('Doe', $data['customerlastname']);
        $this->assertEquals('+359888123456', $data['customerphone']);
    }
    
    public function testResponseStatusChecking()
    {
        // Test successful response
        $successData = array('Status' => '00', 'StatusMsg' => 'Success');
        $successResponse = new MyPOS_SDK_Response($successData);
        
        $this->assertTrue($successResponse->isSuccessful());
        $this->assertFalse($successResponse->isCancelled());
        $this->assertFalse($successResponse->isFailed());
        $this->assertEquals('00', $successResponse->getStatus());
        $this->assertEquals('Success', $successResponse->getStatusMessage());
        
        // Test cancelled response
        $cancelData = array('Status' => '01', 'StatusMsg' => 'Cancelled');
        $cancelResponse = new MyPOS_SDK_Response($cancelData);
        
        $this->assertFalse($cancelResponse->isSuccessful());
        $this->assertTrue($cancelResponse->isCancelled());
        $this->assertFalse($cancelResponse->isFailed());
        
        // Test failed response
        $failData = array('Status' => '05', 'StatusMsg' => 'Insufficient funds');
        $failResponse = new MyPOS_SDK_Response($failData);
        
        $this->assertFalse($failResponse->isSuccessful());
        $this->assertFalse($failResponse->isCancelled());
        $this->assertTrue($failResponse->isFailed());
        $this->assertEquals('Insufficient funds', $failResponse->getErrorMessage());
    }
    
    public function testResponseDataExtraction()
    {
        $responseData = array(
            'Status' => '00',
            'IPC_Trnref' => '123456789',
            'OrderID' => 'test_order_123',
            'Amount' => 1000,
            'Currency' => 'BGN'
        );
        
        $response = new MyPOS_SDK_Response($responseData);
        
        $this->assertEquals('123456789', $response->getTransactionReference());
        $this->assertEquals('test_order_123', $response->getOrderId());
        $this->assertEquals(10.00, $response->getAmount()); // Amount in cents converted to currency
        $this->assertEquals('BGN', $response->getCurrency());
    }
    
    public function testCheckoutUrls()
    {
        $this->assertEquals(MyPOS_SDK_Purchase::SANDBOX_URL, $this->purchase->getCheckoutUrl(true));
        $this->assertEquals(MyPOS_SDK_Purchase::PRODUCTION_URL, $this->purchase->getCheckoutUrl(false));
    }
}
