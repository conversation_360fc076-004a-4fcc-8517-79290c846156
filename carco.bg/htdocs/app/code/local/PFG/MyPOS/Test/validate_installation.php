<?php
/**
 * MyPOS Installation Validation Script
 * 
 * Run this script to validate that the MyPOS payment method is properly installed
 * Usage: php validate_installation.php
 */

// Set up Magento environment
require_once dirname(__FILE__) . '/../../../../Mage.php';
Mage::app();

class MyPOS_Installation_Validator
{
    private $errors = array();
    private $warnings = array();
    private $success = array();
    
    public function validate()
    {
        echo "MyPOS Payment Method Installation Validation\n";
        echo "==========================================\n\n";
        
        $this->checkModuleRegistration();
        $this->checkModuleFiles();
        $this->checkDatabaseTables();
        $this->checkPaymentMethodConfiguration();
        $this->checkSDK();
        $this->checkTemplates();
        $this->checkGraphQLIntegration();
        $this->checkReactIntegration();
        
        $this->displayResults();
    }
    
    private function checkModuleRegistration()
    {
        echo "Checking module registration...\n";
        
        // Check if module is registered
        $moduleFile = Mage::getBaseDir('etc') . DS . 'modules' . DS . 'PFG_MyPOS.xml';
        if (file_exists($moduleFile)) {
            $this->success[] = "Module declaration file exists: {$moduleFile}";
        } else {
            $this->errors[] = "Module declaration file missing: {$moduleFile}";
        }
        
        // Check if module is active
        if (Mage::getConfig()->getModuleConfig('PFG_MyPOS')->is('active', 'true')) {
            $this->success[] = "Module PFG_MyPOS is active";
        } else {
            $this->errors[] = "Module PFG_MyPOS is not active";
        }
    }
    
    private function checkModuleFiles()
    {
        echo "Checking module files...\n";
        
        $requiredFiles = array(
            'etc/config.xml',
            'etc/system.xml',
            'etc/adminhtml.xml',
            'Model/Payment/Mypos.php',
            'Model/Transaction.php',
            'Helper/Data.php',
            'Helper/Mypos.php',
            'Block/Checkout/Form.php',
            'Block/Checkout/Info.php',
            'controllers/RequestController.php',
            'controllers/ResponseController.php',
            'sql/pfg_mypos_setup/install-0.1.0.php'
        );
        
        $moduleDir = Mage::getBaseDir('code') . DS . 'local' . DS . 'PFG' . DS . 'MyPOS' . DS;
        
        foreach ($requiredFiles as $file) {
            $filePath = $moduleDir . $file;
            if (file_exists($filePath)) {
                $this->success[] = "Required file exists: {$file}";
            } else {
                $this->errors[] = "Required file missing: {$file}";
            }
        }
    }
    
    private function checkDatabaseTables()
    {
        echo "Checking database tables...\n";
        
        try {
            $resource = Mage::getSingleton('core/resource');
            $connection = $resource->getConnection('core_read');
            $tableName = $resource->getTableName('pfg_mypos/transactions');
            
            $tableExists = $connection->showTableStatus($tableName);
            if ($tableExists) {
                $this->success[] = "Database table exists: {$tableName}";
                
                // Check table structure
                $columns = $connection->describeTable($tableName);
                $requiredColumns = array(
                    'transaction_id', 'order_id', 'order_increment_id', 'order_reference',
                    'mypos_transaction_id', 'amount', 'currency', 'status', 'created_at'
                );
                
                foreach ($requiredColumns as $column) {
                    if (isset($columns[$column])) {
                        $this->success[] = "Required column exists: {$column}";
                    } else {
                        $this->errors[] = "Required column missing: {$column}";
                    }
                }
            } else {
                $this->errors[] = "Database table missing: {$tableName}";
            }
        } catch (Exception $e) {
            $this->errors[] = "Database check failed: " . $e->getMessage();
        }
    }
    
    private function checkPaymentMethodConfiguration()
    {
        echo "Checking payment method configuration...\n";
        
        try {
            $paymentMethod = Mage::getModel('pfg_mypos/payment_mypos');
            if ($paymentMethod) {
                $this->success[] = "Payment method model loads successfully";
                
                // Check if payment method is available
                if ($paymentMethod->getCode() === 'pfg_mypos') {
                    $this->success[] = "Payment method code is correct: pfg_mypos";
                } else {
                    $this->errors[] = "Payment method code is incorrect";
                }
                
                // Check configuration fields
                $configFields = array('title', 'active', 'sort_order');
                foreach ($configFields as $field) {
                    $value = $paymentMethod->getConfigData($field);
                    if ($value !== null) {
                        $this->success[] = "Configuration field '{$field}' is accessible";
                    } else {
                        $this->warnings[] = "Configuration field '{$field}' may not be set";
                    }
                }
            } else {
                $this->errors[] = "Payment method model failed to load";
            }
        } catch (Exception $e) {
            $this->errors[] = "Payment method check failed: " . $e->getMessage();
        }
    }
    
    private function checkSDK()
    {
        echo "Checking MyPOS SDK...\n";
        
        $sdkPath = Mage::getBaseDir('code') . DS . 'local' . DS . 'PFG' . DS . 'MyPOS' . DS . 'lib' . DS . 'MyPOS' . DS . 'autoload.php';
        
        if (file_exists($sdkPath)) {
            $this->success[] = "SDK autoloader exists";
            
            try {
                require_once $sdkPath;
                
                if (class_exists('MyPOS_SDK_Purchase')) {
                    $this->success[] = "MyPOS_SDK_Purchase class is available";
                } else {
                    $this->errors[] = "MyPOS_SDK_Purchase class not found";
                }
                
                if (class_exists('MyPOS_SDK_Response')) {
                    $this->success[] = "MyPOS_SDK_Response class is available";
                } else {
                    $this->errors[] = "MyPOS_SDK_Response class not found";
                }
            } catch (Exception $e) {
                $this->errors[] = "SDK loading failed: " . $e->getMessage();
            }
        } else {
            $this->errors[] = "SDK autoloader missing: {$sdkPath}";
        }
    }
    
    private function checkTemplates()
    {
        echo "Checking templates...\n";
        
        $templateDir = Mage::getBaseDir('design') . DS . 'frontend' . DS . 'base' . DS . 'default' . DS . 'template' . DS . 'pfg' . DS . 'mypos' . DS;
        
        $requiredTemplates = array(
            'checkout/form.phtml',
            'checkout/info.phtml',
            'redirect.phtml'
        );
        
        foreach ($requiredTemplates as $template) {
            $templatePath = $templateDir . $template;
            if (file_exists($templatePath)) {
                $this->success[] = "Template exists: {$template}";
            } else {
                $this->errors[] = "Template missing: {$template}";
            }
        }
    }
    
    private function checkGraphQLIntegration()
    {
        echo "Checking GraphQL integration...\n";
        
        $salesControllerPath = Mage::getBaseDir('code') . DS . 'local' . DS . 'PFG' . DS . 'Theme' . DS . 'controllers' . DS . 'SalesController.php';
        
        if (file_exists($salesControllerPath)) {
            $content = file_get_contents($salesControllerPath);
            if (strpos($content, 'mypos') !== false && strpos($content, 'pfg_mypos') !== false) {
                $this->success[] = "GraphQL integration: MyPOS mapping found in SalesController";
            } else {
                $this->warnings[] = "GraphQL integration: MyPOS mapping may be missing in SalesController";
            }
        } else {
            $this->warnings[] = "GraphQL SalesController not found - may be in different location";
        }
    }
    
    private function checkReactIntegration()
    {
        echo "Checking React frontend integration...\n";
        
        $paymentDetailsPath = Mage::getBaseDir() . DS . 'carco.bg' . DS . 'frontend' . DS . 'site' . DS . 'app' . DS . 'checkout' . DS . 'onepage' . DS . 'form' . DS . '3_PaymentDetails.tsx';
        
        if (file_exists($paymentDetailsPath)) {
            $content = file_get_contents($paymentDetailsPath);
            if (strpos($content, 'mypos') !== false) {
                $this->success[] = "React integration: MyPOS option found in PaymentDetails component";
            } else {
                $this->warnings[] = "React integration: MyPOS option may be missing in PaymentDetails component";
            }
        } else {
            $this->warnings[] = "React PaymentDetails component not found - may be in different location";
        }
        
        $typesPath = Mage::getBaseDir() . DS . 'carco.bg' . DS . 'frontend' . DS . 'site' . DS . 'app' . DS . 'checkout' . DS . 'onepage' . DS . '_lib' . DS . '_types.ts';
        
        if (file_exists($typesPath)) {
            $content = file_get_contents($typesPath);
            if (strpos($content, 'mypos') !== false) {
                $this->success[] = "React integration: MyPOS type found in types definition";
            } else {
                $this->warnings[] = "React integration: MyPOS type may be missing in types definition";
            }
        } else {
            $this->warnings[] = "React types file not found - may be in different location";
        }
    }
    
    private function displayResults()
    {
        echo "\n\nValidation Results:\n";
        echo "==================\n\n";
        
        if (!empty($this->success)) {
            echo "✓ SUCCESS (" . count($this->success) . " items):\n";
            foreach ($this->success as $message) {
                echo "  ✓ {$message}\n";
            }
            echo "\n";
        }
        
        if (!empty($this->warnings)) {
            echo "⚠ WARNINGS (" . count($this->warnings) . " items):\n";
            foreach ($this->warnings as $message) {
                echo "  ⚠ {$message}\n";
            }
            echo "\n";
        }
        
        if (!empty($this->errors)) {
            echo "✗ ERRORS (" . count($this->errors) . " items):\n";
            foreach ($this->errors as $message) {
                echo "  ✗ {$message}\n";
            }
            echo "\n";
        }
        
        echo "Summary:\n";
        echo "--------\n";
        echo "Success: " . count($this->success) . "\n";
        echo "Warnings: " . count($this->warnings) . "\n";
        echo "Errors: " . count($this->errors) . "\n\n";
        
        if (empty($this->errors)) {
            echo "🎉 Installation validation completed successfully!\n";
            if (!empty($this->warnings)) {
                echo "Note: Please review the warnings above.\n";
            }
        } else {
            echo "❌ Installation validation failed. Please fix the errors above.\n";
        }
    }
}

// Run validation
$validator = new MyPOS_Installation_Validator();
$validator->validate();
