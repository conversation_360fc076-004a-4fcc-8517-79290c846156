<?php

$installer = $this;
$installer->startSetup();

/**
 * Create table 'pfg_mypos_transactions'
 */
$table = $installer->getConnection()
    ->newTable($installer->getTable('pfg_mypos/transactions'))
    ->addColumn('transaction_id', Varien_Db_Ddl_Table::TYPE_INTEGER, null, array(
        'identity'  => true,
        'unsigned'  => true,
        'nullable'  => false,
        'primary'   => true,
    ), 'Transaction ID')
    ->addColumn('order_id', Varien_Db_Ddl_Table::TYPE_INTEGER, null, array(
        'unsigned'  => true,
        'nullable'  => false,
    ), 'Order ID')
    ->addColumn('order_increment_id', Varien_Db_Ddl_Table::TYPE_TEXT, 50, array(
        'nullable'  => false,
    ), 'Order Increment ID')
    ->addColumn('order_reference', Varien_Db_Ddl_Table::TYPE_TEXT, 100, array(
        'nullable'  => false,
    ), 'Order Reference')
    ->addColumn('mypos_transaction_id', Varien_Db_Ddl_Table::TYPE_TEXT, 100, array(
        'nullable'  => true,
    ), 'MyPOS Transaction ID')
    ->addColumn('amount', Varien_Db_Ddl_Table::TYPE_DECIMAL, '12,4', array(
        'nullable'  => false,
        'default'   => '0.0000',
    ), 'Transaction Amount')
    ->addColumn('currency', Varien_Db_Ddl_Table::TYPE_TEXT, 3, array(
        'nullable'  => false,
        'default'   => 'BGN',
    ), 'Currency Code')
    ->addColumn('status', Varien_Db_Ddl_Table::TYPE_TEXT, 20, array(
        'nullable'  => false,
        'default'   => 'pending',
    ), 'Transaction Status')
    ->addColumn('request_data', Varien_Db_Ddl_Table::TYPE_TEXT, '64k', array(
        'nullable'  => true,
    ), 'Request Data (JSON)')
    ->addColumn('response_data', Varien_Db_Ddl_Table::TYPE_TEXT, '64k', array(
        'nullable'  => true,
    ), 'Response Data (JSON)')
    ->addColumn('error_message', Varien_Db_Ddl_Table::TYPE_TEXT, 500, array(
        'nullable'  => true,
    ), 'Error Message')
    ->addColumn('refund_amount', Varien_Db_Ddl_Table::TYPE_DECIMAL, '12,4', array(
        'nullable'  => true,
    ), 'Refund Amount')
    ->addColumn('refund_transaction_id', Varien_Db_Ddl_Table::TYPE_TEXT, 100, array(
        'nullable'  => true,
    ), 'Refund Transaction ID')
    ->addColumn('created_at', Varien_Db_Ddl_Table::TYPE_TIMESTAMP, null, array(
        'nullable'  => false,
        'default'   => Varien_Db_Ddl_Table::TIMESTAMP_INIT,
    ), 'Created At')
    ->addColumn('updated_at', Varien_Db_Ddl_Table::TYPE_TIMESTAMP, null, array(
        'nullable'  => true,
    ), 'Updated At')
    ->addIndex($installer->getIdxName('pfg_mypos/transactions', array('order_id')),
        array('order_id'))
    ->addIndex($installer->getIdxName('pfg_mypos/transactions', array('order_increment_id')),
        array('order_increment_id'))
    ->addIndex($installer->getIdxName('pfg_mypos/transactions', array('order_reference')),
        array('order_reference'))
    ->addIndex($installer->getIdxName('pfg_mypos/transactions', array('mypos_transaction_id')),
        array('mypos_transaction_id'))
    ->addIndex($installer->getIdxName('pfg_mypos/transactions', array('status')),
        array('status'))
    ->addIndex($installer->getIdxName('pfg_mypos/transactions', array('created_at')),
        array('created_at'))
    ->addForeignKey(
        $installer->getFkName('pfg_mypos/transactions', 'order_id', 'sales/order', 'entity_id'),
        'order_id',
        $installer->getTable('sales/order'),
        'entity_id',
        Varien_Db_Ddl_Table::ACTION_CASCADE,
        Varien_Db_Ddl_Table::ACTION_CASCADE
    )
    ->setComment('MyPOS Transactions Table');

$installer->getConnection()->createTable($table);

$installer->endSetup();
