<?php

// Load MyPOS SDK
require_once Mage::getBaseDir('code') . '/local/PFG/MyPOS/lib/MyPOS/autoload.php';

class PFG_MyPOS_Helper_Mypos extends Mage_Core_Helper_Abstract
{
    const SANDBOX_URL = 'https://mypos.com/vmp/checkout-test';
    const PRODUCTION_URL = 'https://mypos.com/vmp/checkout';

    const SANDBOX_API_URL = 'https://mypos.com/vmp/checkout-test/';
    const PRODUCTION_API_URL = 'https://mypos.com/vmp/checkout/';

    /**
     * Get MyPOS checkout URL
     *
     * @return string
     */
    public function getCheckoutUrl()
    {
        $paymentMethod = Mage::helper('pfg_mypos')->getPaymentMethod();
        $isSandbox = $paymentMethod->isSandboxMode();
        Mage::log('MyPOS sandbox mode check: ' . ($isSandbox ? 'TRUE' : 'FALSE'), Zend_Log::INFO, 'mypos_debug.log');

        $url = $isSandbox ? self::SANDBOX_URL : self::PRODUCTION_URL;
        Mage::log('MyPOS checkout URL: ' . $url, Zend_Log::INFO, 'mypos_debug.log');

        return $url;
    }

    /**
     * Get MyPOS API URL
     *
     * @return string
     */
    public function getApiUrl()
    {
        $paymentMethod = Mage::helper('pfg_mypos')->getPaymentMethod();
        return $paymentMethod->isSandboxMode() ? self::SANDBOX_API_URL : self::PRODUCTION_API_URL;
    }

    /**
     * Create purchase request for MyPOS
     *
     * @param Mage_Sales_Model_Order $order
     * @return array
     */
    public function createPurchaseRequest($order)
    {
        Mage::log('MyPOS createPurchaseRequest called', Zend_Log::INFO, 'mypos_debug.log');

        try {
            $helper = Mage::helper('pfg_mypos');
            Mage::log('MyPOS data helper loaded', Zend_Log::INFO, 'mypos_debug.log');

            $paymentMethod = $helper->getPaymentMethod();
            Mage::log('MyPOS payment method loaded', Zend_Log::INFO, 'mypos_debug.log');

            $amount = $helper->formatAmount($order->getGrandTotal());
            Mage::log('MyPOS amount formatted: ' . $amount, Zend_Log::INFO, 'mypos_debug.log');

            $currency = $paymentMethod->getCurrency();
            Mage::log('MyPOS currency: ' . $currency, Zend_Log::INFO, 'mypos_debug.log');

            $orderReference = $helper->generateOrderReference($order);
            Mage::log('MyPOS order reference: ' . $orderReference, Zend_Log::INFO, 'mypos_debug.log');

            // Create MyPOS Purchase object
            Mage::log('Creating MyPOS Purchase object', Zend_Log::INFO, 'mypos_debug.log');

            // Ensure SDK is loaded - include Purchase class directly
            $purchasePath = Mage::getBaseDir() . '/app/code/local/PFG/MyPOS/lib/MyPOS/SDK/Purchase.php';
            Mage::log('Purchase class path: ' . $purchasePath, Zend_Log::INFO, 'mypos_debug.log');
            Mage::log('Purchase class exists: ' . (file_exists($purchasePath) ? 'YES' : 'NO'), Zend_Log::INFO, 'mypos_debug.log');

            if (file_exists($purchasePath)) {
                require_once $purchasePath;
                Mage::log('Purchase class included', Zend_Log::INFO, 'mypos_debug.log');
            }

            Mage::log('Class exists check: ' . (class_exists('MyPOS_SDK_Purchase') ? 'YES' : 'NO'), Zend_Log::INFO, 'mypos_debug.log');
            Mage::log('About to instantiate MyPOS_SDK_Purchase', Zend_Log::INFO, 'mypos_debug.log');
            $purchase = new MyPOS_SDK_Purchase();
            Mage::log('MyPOS Purchase object created', Zend_Log::INFO, 'mypos_debug.log');

            $purchase->setStoreId($paymentMethod->getStoreId())
                     ->setWalletNumber($paymentMethod->getWalletNumber())
                     ->setAmount($amount)
                     ->setCurrency($currency)
                     ->setOrderId($orderReference)
                     ->setLanguage($paymentMethod->getLanguage())
                     ->setUrlOk($helper->getSuccessUrl())
                     ->setUrlCancel($helper->getCancelUrl())
                     ->setUrlNotify($helper->getNotifyUrl())
                     ->setCustomerEmail($order->getCustomerEmail())
                     ->setCustomerFirstName($order->getBillingAddress()->getFirstname())
                     ->setCustomerLastName($order->getBillingAddress()->getLastname())
                     ->setCustomerPhone($order->getBillingAddress()->getTelephone())
                     ->setCartItems($this->getCartItems($order))
                     ->setNote($this->getOrderNote($order))
                     ->setOutputFormat('json');
            Mage::log('MyPOS Purchase object configured', Zend_Log::INFO, 'mypos_debug.log');

            // Get form data with signature
            Mage::log('About to generate form data with signature', Zend_Log::INFO, 'mypos_debug.log');
            $keyIndex = $paymentMethod->getConfigData('key_index');
            Mage::log('MyPOS key index: ' . $keyIndex, Zend_Log::INFO, 'mypos_debug.log');

            $formData = $purchase->getFormData(
                $paymentMethod->getPrivateKeyPath(),
                $paymentMethod->getPrivateKeyPassword(),
                $paymentMethod->isSandboxMode(),
                $keyIndex
            );
            Mage::log('MyPOS form data generated successfully', Zend_Log::INFO, 'mypos_debug.log');
            Mage::log('MyPOS form data: ' . print_r($formData, true), Zend_Log::INFO, 'mypos_debug.log');

        // Store transaction data
        $this->storeTransactionData($order, $orderReference, $formData['data']);
        Mage::log('MyPOS transaction data stored', Zend_Log::INFO, 'mypos_debug.log');

        return $formData;

        } catch (Exception $e) {
            Mage::log('MyPOS createPurchaseRequest error: ' . $e->getMessage(), Zend_Log::ERR, 'mypos_debug.log');
            Mage::log('MyPOS error stack: ' . $e->getTraceAsString(), Zend_Log::ERR, 'mypos_debug.log');
            throw $e;
        }
    }

    /**
     * Get cart items for MyPOS request
     *
     * @param Mage_Sales_Model_Order $order
     * @return string
     */
    protected function getCartItems($order)
    {
        $items = array();
        $helper = Mage::helper('pfg_mypos');
        
        foreach ($order->getAllVisibleItems() as $item) {
            $items[] = array(
                'name' => $item->getName(),
                'quantity' => (int)$item->getQtyOrdered(),
                'price' => $helper->formatAmount($item->getPrice())
            );
        }
        
        // Add shipping if present
        if ($order->getShippingAmount() > 0) {
            $items[] = array(
                'name' => 'Shipping - ' . $order->getShippingDescription(),
                'quantity' => 1,
                'price' => $helper->formatAmount($order->getShippingAmount())
            );
        }
        
        // Add tax if present
        if ($order->getTaxAmount() > 0) {
            $items[] = array(
                'name' => 'Tax',
                'quantity' => 1,
                'price' => $helper->formatAmount($order->getTaxAmount())
            );
        }
        
        return json_encode($items);
    }

    /**
     * Get order note
     *
     * @param Mage_Sales_Model_Order $order
     * @return string
     */
    protected function getOrderNote($order)
    {
        $paymentMethod = Mage::helper('pfg_mypos')->getPaymentMethod();
        $description = $paymentMethod->getConfigData('description');
        
        if (empty($description)) {
            $description = 'Order #' . $order->getIncrementId();
        }
        
        return $description;
    }

    /**
     * Generate signature for request
     *
     * @param array $data
     * @return string
     */
    public function generateSignature($data)
    {
        try {
            $paymentMethod = Mage::helper('pfg_mypos')->getPaymentMethod();
            $privateKeyPath = $paymentMethod->getPrivateKeyPath();
            $privateKeyPassword = $paymentMethod->getPrivateKeyPassword();
            
            if (!file_exists($privateKeyPath)) {
                // Enhanced error message with debugging info
                $debugInfo = array(
                    'Expected path' => $privateKeyPath,
                    'Current working directory' => getcwd(),
                    'Base dir (var)' => Mage::getBaseDir('var'),
                    'Config path' => $paymentMethod->getConfigData('private_key_path'),
                    'File exists check' => file_exists($privateKeyPath) ? 'YES' : 'NO'
                );

                $errorMsg = 'Private key file does not exist. Debug info: ' . json_encode($debugInfo);
                Mage::helper('pfg_mypos')->log($errorMsg, Zend_Log::ERR);
                throw new Exception('Private key file does not exist.');
            }
            
            $privateKey = file_get_contents($privateKeyPath);
            
            if ($privateKeyPassword) {
                $privateKey = openssl_pkey_get_private($privateKey, $privateKeyPassword);
            } else {
                $privateKey = openssl_pkey_get_private($privateKey);
            }
            
            if (!$privateKey) {
                throw new Exception('Could not load private key');
            }
            
            $dataString = $this->buildSignatureString($data);
            $signature = '';
            
            if (!openssl_sign($dataString, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {
                throw new Exception('Could not generate signature');
            }
            
            return base64_encode($signature);
            
        } catch (Exception $e) {
            Mage::helper('pfg_mypos')->logException($e);
            throw $e;
        }
    }

    /**
     * Build signature string from data
     *
     * @param array $data
     * @return string
     */
    protected function buildSignatureString($data)
    {
        Mage::log('*** HELPER buildSignatureString() CALLED ***', Zend_Log::INFO, 'mypos_debug.log');

        // Remove signature field if present
        unset($data['Signature']);

        // Sort by key (case-sensitive) - this is critical for MyPOS signature validation
        ksort($data);

        // According to MyPOS documentation: concatenate all VALUES with dashes, then Base64 encode
        // IMPORTANT: Exclude empty values as they cause signature validation to fail
        $values = array();
        foreach ($data as $key => $value) {
            // Handle different value types for signature
            if (is_array($value)) {
                $value = json_encode($value, JSON_UNESCAPED_UNICODE);
            }

            // Convert to string and skip empty values
            $stringValue = (string)$value;
            if ($stringValue !== '') {
                $values[] = $stringValue;
            }
        }

        // Concatenate values with dashes as per MyPOS documentation
        $concatenatedValues = implode('-', $values);

        // Base64 encode the concatenated string as per MyPOS documentation
        Mage::log('*** HELPER Signature Data: ' . print_r($data, true), Zend_Log::INFO, 'mypos_debug.log');
        Mage::log('*** HELPER Signature String: ' . base64_encode($concatenatedValues), Zend_Log::INFO, 'mypos_debug.log');

        return base64_encode($concatenatedValues);
    }

    /**
     * Store transaction data
     *
     * @param Mage_Sales_Model_Order $order
     * @param string $orderReference
     * @param array $requestData
     */
    protected function storeTransactionData($order, $orderReference, $requestData)
    {
        try {
            $transaction = Mage::getModel('pfg_mypos/transaction');
            $transaction->setOrderId($order->getId())
                       ->setOrderIncrementId($order->getIncrementId())
                       ->setOrderReference($orderReference)
                       ->setAmount($order->getGrandTotal())
                       ->setCurrency($requestData['Currency'])
                       ->setStatus('pending')
                       ->setRequestData(json_encode($requestData))
                       ->setCreatedAt(now())
                       ->save();
                       
        } catch (Exception $e) {
            Mage::helper('pfg_mypos')->logException($e);
        }
    }

    /**
     * Process refund request
     *
     * @param array $refundData
     * @return array
     */
    public function processRefund($refundData)
    {
        try {
            $apiUrl = $this->getApiUrl() . 'IPCRefund';
            
            // Add signature to refund data
            $refundData['Signature'] = $this->generateSignature($refundData);
            
            // Make API call
            $response = $this->makeApiCall($apiUrl, $refundData);
            
            if ($response && isset($response['Status']) && $response['Status'] == '00') {
                return array(
                    'success' => true,
                    'transaction_id' => $response['IPC_Trnref'],
                    'message' => 'Refund processed successfully'
                );
            } else {
                return array(
                    'success' => false,
                    'message' => isset($response['StatusMsg']) ? $response['StatusMsg'] : 'Refund failed'
                );
            }
            
        } catch (Exception $e) {
            Mage::helper('pfg_mypos')->logException($e);
            return array(
                'success' => false,
                'message' => $e->getMessage()
            );
        }
    }

    /**
     * Get refund request data
     *
     * @param Mage_Sales_Model_Order $order
     * @param float $amount
     * @return array
     */
    public function getRefundRequest($order, $amount)
    {
        $helper = Mage::helper('pfg_mypos');
        $paymentMethod = $helper->getPaymentMethod();
        
        return array(
            'IPCmethod' => 'IPCRefund',
            'IPCVersion' => '1.4',
            'SID' => $paymentMethod->getStoreId(),
            'WalletNumber' => $paymentMethod->getWalletNumber(),
            'IPC_Trnref' => $order->getPayment()->getTransactionId(),
            'Amount' => $helper->formatAmount($amount),
            'Currency' => $paymentMethod->getCurrency(),
            'OutputFormat' => 'json'
        );
    }

    /**
     * Make API call to MyPOS
     *
     * @param string $url
     * @param array $data
     * @return array|false
     */
    public function makeApiCall($url, $data)
    {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Content-Type: application/x-www-form-urlencoded',
                'User-Agent: Magento-MyPOS/1.0'
            ));

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            if (curl_error($ch)) {
                throw new Exception('cURL error: ' . curl_error($ch));
            }

            curl_close($ch);

            if ($httpCode !== 200) {
                throw new Exception('HTTP error: ' . $httpCode);
            }

            return json_decode($response, true);

        } catch (Exception $e) {
            Mage::helper('pfg_mypos')->logException($e);
            return false;
        }
    }
}
