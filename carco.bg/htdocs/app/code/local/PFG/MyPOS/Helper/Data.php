<?php

class PFG_MyPOS_Helper_Data extends Mage_Core_Helper_Abstract
{
    const LOG_FILE = 'pfg_mypos.log';
    const EXCEPTION_LOG_FILE = 'pfg_mypos_exceptions.log';

    /**
     * Log message to MyPOS log file
     *
     * @param string $message
     * @param int $level
     * @param string $file
     * @param bool $forceLog
     */
    public function log($message, $level = null, $file = '', $forceLog = false)
    {
        if (!$forceLog && !$this->isLogEnabled()) {
            return;
        }

        if (empty($file)) {
            $file = self::LOG_FILE;
        }

        Mage::log($message, $level, $file, $forceLog);
    }

    /**
     * Log exception
     *
     * @param Exception $e
     */
    public function logException(Exception $e)
    {
        $this->log($e->getMessage(), Zend_Log::ERR, self::EXCEPTION_LOG_FILE, true);
        $this->log($e->getTraceAsString(), Zend_Log::ERR, self::EXCEPTION_LOG_FILE, true);
    }

    /**
     * Check if logging is enabled
     *
     * @return bool
     */
    public function isLogEnabled()
    {
        return Mage::getStoreConfigFlag('pfg_mypos/advanced/force_log');
    }

    /**
     * Get payment method instance
     *
     * @return PFG_MyPOS_Model_Payment_Mypos
     */
    public function getPaymentMethod()
    {
        return Mage::getModel('pfg_mypos/payment_mypos');
    }

    /**
     * Get order by increment ID
     *
     * @param string $incrementId
     * @return Mage_Sales_Model_Order|false
     */
    public function getOrderByIncrementId($incrementId)
    {
        $order = Mage::getModel('sales/order')->loadByIncrementId($incrementId);
        return $order->getId() ? $order : false;
    }

    /**
     * Format amount for MyPOS (in cents)
     *
     * @param float $amount
     * @return int
     */
    public function formatAmount($amount)
    {
        return (int)round($amount * 100);
    }

    /**
     * Format amount from MyPOS (from cents)
     *
     * @param int $amount
     * @return float
     */
    public function formatAmountFromCents($amount)
    {
        return (float)($amount / 100);
    }

    /**
     * Generate unique order reference
     *
     * @param Mage_Sales_Model_Order $order
     * @return string
     */
    public function generateOrderReference($order)
    {
        return $order->getIncrementId() . '_' . time();
    }

    /**
     * Validate MyPOS response signature
     *
     * @param array $data
     * @param string $signature
     * @return bool
     */
    public function validateSignature($data, $signature)
    {
        try {
            $paymentMethod = $this->getPaymentMethod();
            $publicKeyPath = $paymentMethod->getPublicKeyPath();
            
            if (!file_exists($publicKeyPath)) {
                $this->log('Public key file not found: ' . $publicKeyPath, Zend_Log::ERR);
                return false;
            }

            $publicKey = file_get_contents($publicKeyPath);
            $dataString = $this->buildDataString($data);
            
            return openssl_verify($dataString, base64_decode($signature), $publicKey, OPENSSL_ALGO_SHA256) === 1;
            
        } catch (Exception $e) {
            $this->logException($e);
            return false;
        }
    }

    /**
     * Build data string for signature verification
     *
     * @param array $data
     * @return string
     */
    public function buildDataString($data)
    {
        // Remove signature field if present (case insensitive)
        unset($data['signature']);
        unset($data['Signature']);

        // Sort by key (case-sensitive) - this is critical for MyPOS signature validation
        ksort($data);

        // According to MyPOS documentation: concatenate all VALUES with dashes, then Base64 encode
        $values = array();
        foreach ($data as $key => $value) {
            // Handle different value types for signature
            if (is_array($value)) {
                $value = json_encode($value, JSON_UNESCAPED_UNICODE);
            }
            $values[] = $value;
        }

        // Concatenate values with dashes as per MyPOS documentation
        $concatenatedValues = implode('-', $values);

        // Base64 encode the concatenated string as per MyPOS documentation
        return base64_encode($concatenatedValues);
    }

    /**
     * Get success URL
     *
     * @return string
     */
    public function getSuccessUrl()
    {
        return Mage::getUrl('pfg_mypos/response/success', array('_secure' => true));
    }

    /**
     * Get cancel URL
     *
     * @return string
     */
    public function getCancelUrl()
    {
        return Mage::getUrl('pfg_mypos/response/cancel', array('_secure' => true));
    }

    /**
     * Get notify URL
     *
     * @return string
     */
    public function getNotifyUrl()
    {
        return Mage::getUrl('pfg_mypos/response/notify', array('_secure' => true));
    }

    /**
     * Check if order can be processed with MyPOS
     *
     * @param Mage_Sales_Model_Order $order
     * @return bool
     */
    public function canProcessOrder($order)
    {
        if (!$order || !$order->getId()) {
            return false;
        }

        if ($order->getPayment()->getMethod() !== PFG_MyPOS_Model_Payment_Mypos::PAYMENT_METHOD_CODE) {
            return false;
        }

        return true;
    }

    /**
     * Get order status after payment
     *
     * @return string
     */
    public function getOrderStatusAfterPayment()
    {
        return $this->getPaymentMethod()->getConfigData('order_status_after_payment') ?: 'processing';
    }

    /**
     * Get new order status
     *
     * @return string
     */
    public function getNewOrderStatus()
    {
        return $this->getPaymentMethod()->getConfigData('new_order_status') ?: 'pending';
    }
}
