<?php
/**
 * Simple MyPOS Configuration Pack Parser
 * No Magento dependencies required
 */

class Simple_MyPOS_ConfigPack_Parser
{
    public function parseConfigPack($configPack)
    {
        echo "MyPOS Configuration Pack Parser\n";
        echo "==============================\n\n";
        
        if (empty($configPack)) {
            echo "❌ Error: Configuration Pack is empty\n";
            return false;
        }
        
        try {
            echo "📦 Parsing Configuration Pack...\n";
            echo "Pack length: " . strlen($configPack) . " characters\n\n";
            
            // Decode base64
            $decoded = base64_decode($configPack, true);
            if ($decoded === false) {
                echo "❌ Error: Invalid base64 encoding\n";
                return false;
            }
            
            echo "✅ Successfully decoded base64 data\n";
            
            // Parse JSON
            $jsonData = json_decode($decoded, true);
            if ($jsonData === null) {
                echo "❌ Error: Invalid JSON data\n";
                return false;
            }
            
            echo "✅ Successfully parsed JSON data\n\n";
            
            return $this->extractFromJson($jsonData);
            
        } catch (Exception $e) {
            echo "❌ Error parsing configuration pack: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    private function extractFromJson($jsonData)
    {
        echo "📋 Extracting data from JSON format...\n\n";
        
        $config = array();
        
        // Map JSON fields to our config
        $fieldMappings = array(
            'store_id' => array('sid', 'store_id', 'storeId', 'SID'),
            'client_number' => array('cn', 'client_number', 'clientNumber', 'wallet_number'),
            'private_key' => array('pk', 'private_key', 'privateKey'),
            'public_key' => array('pc', 'public_key', 'publicKey', 'certificate'),
            'key_index' => array('idx', 'key_index', 'keyIndex', 'index')
        );
        
        foreach ($fieldMappings as $configKey => $possibleKeys) {
            foreach ($possibleKeys as $key) {
                if (isset($jsonData[$key])) {
                    $config[$configKey] = $jsonData[$key];
                    echo "✅ Found {$configKey}: " . substr($jsonData[$key], 0, 50) . "...\n";
                    break;
                }
            }
        }
        
        return $this->validateAndSaveConfig($config);
    }
    
    private function validateAndSaveConfig($config)
    {
        echo "\n🔍 Validating configuration...\n";
        
        $required = array('private_key', 'public_key', 'store_id', 'client_number');
        $missing = array();
        
        foreach ($required as $field) {
            if (empty($config[$field])) {
                $missing[] = $field;
            }
        }
        
        if (!empty($missing)) {
            echo "❌ Missing required fields: " . implode(', ', $missing) . "\n";
            echo "\nFound fields:\n";
            foreach ($config as $key => $value) {
                if (!empty($value)) {
                    echo "  ✅ {$key}: " . substr($value, 0, 50) . "...\n";
                }
            }
            return false;
        }
        
        echo "✅ All required fields found!\n\n";
        
        // Save keys to files
        $this->saveKeysToFiles($config);
        
        // Generate Magento configuration
        $this->generateMagentoConfig($config);
        
        // Compare with your existing credentials
        $this->compareCredentials($config);
        
        return $config;
    }
    
    private function saveKeysToFiles($config)
    {
        echo "💾 Saving keys to files...\n";
        
        // Get the project root directory
        $projectRoot = dirname(dirname(dirname(dirname(dirname(__FILE__)))));
        $varDir = $projectRoot . '/var';
        $myposDir = $varDir . '/mypos';
        
        // Create directory if it doesn't exist
        if (!is_dir($myposDir)) {
            mkdir($myposDir, 0755, true);
            echo "✅ Created directory: {$myposDir}\n";
        }
        
        // Clean and format private key
        $privateKey = str_replace('\\n', "\n", $config['private_key']);
        $privateKeyPath = $myposDir . '/private_key.pem';
        file_put_contents($privateKeyPath, $privateKey);
        chmod($privateKeyPath, 0600);
        echo "✅ Saved private key: {$privateKeyPath}\n";
        
        // Clean and format public key/certificate
        $publicKey = str_replace('\\n', "\n", $config['public_key']);
        $publicKeyPath = $myposDir . '/public_key.pem';
        file_put_contents($publicKeyPath, $publicKey);
        chmod($publicKeyPath, 0644);
        echo "✅ Saved public certificate: {$publicKeyPath}\n";
        
        echo "\n";
    }
    
    private function generateMagentoConfig($config)
    {
        echo "⚙️  Generating Magento configuration...\n";
        
        $configSql = "-- MyPOS Configuration (Generated from Configuration Pack)\n";
        $configSql .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n\n";
        
        $configSql .= "-- Enable MyPOS payment method\n";
        $configSql .= "INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/active', '1') ON DUPLICATE KEY UPDATE value = '1';\n\n";
        
        $configSql .= "-- Set basic configuration\n";
        $configSql .= "INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/title', 'MyPOS Credit Card Payment') ON DUPLICATE KEY UPDATE value = 'MyPOS Credit Card Payment';\n";
        $configSql .= "INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/instructions', 'You will be redirected to MyPOS secure payment page') ON DUPLICATE KEY UPDATE value = 'You will be redirected to MyPOS secure payment page';\n\n";
        
        $configSql .= "-- Set MyPOS credentials\n";
        $configSql .= "INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/store_id', '{$config['store_id']}') ON DUPLICATE KEY UPDATE value = '{$config['store_id']}';\n";
        $configSql .= "INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/client_number', '{$config['client_number']}') ON DUPLICATE KEY UPDATE value = '{$config['client_number']}';\n\n";
        
        if (!empty($config['key_index'])) {
            $configSql .= "-- Set Key Index\n";
            $configSql .= "INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/key_index', '{$config['key_index']}') ON DUPLICATE KEY UPDATE value = '{$config['key_index']}';\n\n";
        }
        
        $configSql .= "-- Set key file paths\n";
        $configSql .= "INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/private_key_path', 'mypos/private_key.pem') ON DUPLICATE KEY UPDATE value = 'mypos/private_key.pem';\n";
        $configSql .= "INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/public_key_path', 'mypos/public_key.pem') ON DUPLICATE KEY UPDATE value = 'mypos/public_key.pem';\n\n";
        
        $configSql .= "-- Set default configuration\n";
        $configSql .= "INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/currency', 'BGN') ON DUPLICATE KEY UPDATE value = 'BGN';\n";
        $configSql .= "INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/language', 'BG') ON DUPLICATE KEY UPDATE value = 'BG';\n";
        $configSql .= "INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/sandbox_mode', '1') ON DUPLICATE KEY UPDATE value = '1';\n";
        $configSql .= "INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/new_order_status', 'pending') ON DUPLICATE KEY UPDATE value = 'pending';\n";
        $configSql .= "INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/order_status_after_payment', 'processing') ON DUPLICATE KEY UPDATE value = 'processing';\n";
        $configSql .= "INSERT INTO core_config_data (scope, scope_id, path, value) VALUES ('default', 0, 'payment/pfg_mypos/sort_order', '110') ON DUPLICATE KEY UPDATE value = '110';\n\n";
        
        $configSql .= "-- Verify configuration\n";
        $configSql .= "SELECT path, value FROM core_config_data WHERE path LIKE 'payment/pfg_mypos/%' ORDER BY path;\n";
        
        $configFile = dirname(__FILE__) . '/mypos_config_from_pack.sql';
        file_put_contents($configFile, $configSql);
        
        echo "✅ Generated SQL configuration: {$configFile}\n";
    }
    
    private function compareCredentials($config)
    {
        echo "\n🔍 Comparing with your existing credentials...\n";
        
        $yourCredentials = array(
            'client_number' => 'HSdDQ8dSl9MVzoMbGxgRVLyt',
            'client_code' => 'DfqEPIicwSZPl7hQw4ONay6SlE6BUtAjKgnBu0DVJIdwsrZB'
        );
        
        echo "Your original credentials:\n";
        echo "  Client Number: {$yourCredentials['client_number']}\n";
        echo "  Client Code: {$yourCredentials['client_code']}\n\n";
        
        echo "Configuration Pack credentials:\n";
        echo "  Store ID: {$config['store_id']}\n";
        echo "  Client Number: {$config['client_number']}\n\n";
        
        // Check if they match
        if ($config['client_number'] === $yourCredentials['client_code']) {
            echo "✅ MATCH: Configuration Pack Client Number matches your Client Code\n";
        } elseif ($config['store_id'] === $yourCredentials['client_number']) {
            echo "✅ MATCH: Configuration Pack Store ID matches your Client Number\n";
        } else {
            echo "⚠️  WARNING: Credentials don't match exactly. This might be normal if:\n";
            echo "   - You have multiple stores in your MyPOS account\n";
            echo "   - The Configuration Pack is for a different store\n";
            echo "   - MyPOS uses different internal IDs\n";
        }
        
        echo "\n🎉 Configuration Pack parsed successfully!\n\n";
        
        echo "📋 Summary:\n";
        echo "  Store ID: {$config['store_id']}\n";
        echo "  Client Number: {$config['client_number']}\n";
        if (!empty($config['key_index'])) {
            echo "  Key Index: {$config['key_index']}\n";
        }
        echo "  Private Key: Saved to var/mypos/private_key.pem\n";
        echo "  Public Certificate: Saved to var/mypos/public_key.pem\n";
        echo "\n📝 Next Steps:\n";
        echo "1. Run the generated SQL file to configure Magento:\n";
        echo "   mysql -u username -p database_name < mypos_config_from_pack.sql\n";
        echo "2. Clear Magento cache\n";
        echo "3. Configure remaining settings in Magento admin\n";
        echo "4. Test in sandbox mode\n";
        echo "5. Go live!\n";
    }
}

// Usage
if (isset($argv[1]) && !empty($argv[1])) {
    $parser = new Simple_MyPOS_ConfigPack_Parser();
    $parser->parseConfigPack($argv[1]);
} else {
    echo "Usage: php simple_config_parser.php 'YOUR_CONFIG_PACK_STRING'\n";
}
