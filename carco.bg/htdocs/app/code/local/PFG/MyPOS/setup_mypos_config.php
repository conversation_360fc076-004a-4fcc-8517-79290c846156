<?php
/**
 * MyPOS Configuration Setup Script
 * 
 * This script helps configure MyPOS payment method with your credentials
 */

require_once dirname(__FILE__) . '/../../../../Mage.php';
Mage::app();

class MyPOS_Config_Setup
{
    // Your MyPOS credentials
    const CLIENT_NUMBER = 'HSdDQ8dSl9MVzoMbGxgRVLyt';
    const CLIENT_CODE = 'DfqEPIicwSZPl7hQw4ONay6SlE6BUtAjKgnBu0DVJIdwsrZB';
    
    public function setupConfiguration()
    {
        echo "MyPOS Payment Method Configuration Setup\n";
        echo "=======================================\n\n";
        
        echo "Your MyPOS Credentials:\n";
        echo "Client Number: " . self::CLIENT_NUMBER . "\n";
        echo "Client Code: " . self::CLIENT_CODE . "\n\n";
        
        // Check if we can determine which is which
        $this->analyzeCredentials();
        $this->checkKeyFiles();
        $this->suggestConfiguration();
        $this->createSampleConfig();
    }
    
    private function analyzeCredentials()
    {
        echo "Analyzing credentials...\n";
        echo "-----------------------\n";
        
        // Typically, Store ID (SID) is shorter and Wallet Number is longer
        $clientNumberLength = strlen(self::CLIENT_NUMBER);
        $clientCodeLength = strlen(self::CLIENT_CODE);
        
        echo "Client Number length: {$clientNumberLength} characters\n";
        echo "Client Code length: {$clientCodeLength} characters\n\n";
        
        // Make educated guess based on typical MyPOS patterns
        if ($clientNumberLength < $clientCodeLength) {
            echo "💡 Recommendation based on length analysis:\n";
            echo "   Store ID (SID): " . self::CLIENT_NUMBER . "\n";
            echo "   Wallet Number: " . self::CLIENT_CODE . "\n\n";
        } else {
            echo "💡 Recommendation based on length analysis:\n";
            echo "   Store ID (SID): " . self::CLIENT_CODE . "\n";
            echo "   Wallet Number: " . self::CLIENT_NUMBER . "\n\n";
        }
        
        echo "⚠️  Please verify these mappings with MyPOS support if unsure.\n\n";
    }
    
    private function checkKeyFiles()
    {
        echo "Checking for key files...\n";
        echo "------------------------\n";
        
        $varDir = Mage::getBaseDir('var');
        $myposDir = $varDir . DS . 'mypos';
        
        // Create mypos directory if it doesn't exist
        if (!is_dir($myposDir)) {
            mkdir($myposDir, 0755, true);
            echo "✓ Created directory: {$myposDir}\n";
        }
        
        $privateKeyPath = $myposDir . DS . 'private_key.pem';
        $publicKeyPath = $myposDir . DS . 'public_key.pem';
        
        if (file_exists($privateKeyPath)) {
            echo "✓ Private key found: {$privateKeyPath}\n";
        } else {
            echo "❌ Private key missing: {$privateKeyPath}\n";
            echo "   Please upload your private key file to this location.\n";
        }
        
        if (file_exists($publicKeyPath)) {
            echo "✓ Public key found: {$publicKeyPath}\n";
        } else {
            echo "❌ Public key missing: {$publicKeyPath}\n";
            echo "   Please upload your public key file to this location.\n";
        }
        
        echo "\n";
    }
    
    private function suggestConfiguration()
    {
        echo "Suggested Magento Configuration:\n";
        echo "-------------------------------\n";
        echo "Go to: System > Configuration > Payment Methods > MyPOS Payment Gateway\n\n";
        
        echo "Basic Settings:\n";
        echo "- Enabled: Yes\n";
        echo "- Title: MyPOS Credit Card Payment\n";
        echo "- Instructions: You will be redirected to MyPOS secure payment page\n";
        echo "- Description: Payment via MyPOS gateway\n\n";
        
        echo "MyPOS API Settings:\n";
        echo "- Store ID (SID): " . self::CLIENT_NUMBER . " (verify with MyPOS)\n";
        echo "- Wallet Number: " . self::CLIENT_CODE . " (verify with MyPOS)\n";
        echo "- Currency: BGN (or EUR/USD as agreed with MyPOS)\n";
        echo "- Language: BG\n\n";
        
        echo "Security Settings:\n";
        echo "- Private Key Path: mypos/private_key.pem\n";
        echo "- Private Key Password: (leave empty if no password)\n";
        echo "- Public Key Path: mypos/public_key.pem\n";
        echo "- Sandbox Mode: Yes (for testing)\n\n";
        
        echo "Order Status Settings:\n";
        echo "- New Order Status: pending\n";
        echo "- Order Status After Payment: processing\n\n";
    }
    
    private function createSampleConfig()
    {
        echo "Creating sample configuration file...\n";
        echo "------------------------------------\n";
        
        $configData = array(
            'payment' => array(
                'pfg_mypos' => array(
                    'active' => '1',
                    'title' => 'MyPOS Credit Card Payment',
                    'instructions' => 'You will be redirected to MyPOS secure payment page to complete your payment.',
                    'description' => 'Payment via MyPOS gateway',
                    'store_id' => self::CLIENT_NUMBER,
                    'wallet_number' => self::CLIENT_CODE,
                    'currency' => 'BGN',
                    'language' => 'BG',
                    'private_key_path' => 'mypos/private_key.pem',
                    'private_key_password' => '',
                    'public_key_path' => 'mypos/public_key.pem',
                    'sandbox_mode' => '1',
                    'new_order_status' => 'pending',
                    'order_status_after_payment' => 'processing',
                    'sort_order' => '110'
                )
            )
        );
        
        $configFile = dirname(__FILE__) . DS . 'mypos_config_sample.json';
        file_put_contents($configFile, json_encode($configData, JSON_PRETTY_PRINT));
        
        echo "✓ Sample configuration saved to: {$configFile}\n";
        echo "  You can use this as reference for manual configuration.\n\n";
    }
}

// Run the setup
$setup = new MyPOS_Config_Setup();
$setup->setupConfiguration();

echo "Next Steps:\n";
echo "----------\n";
echo "1. Contact MyPOS support to obtain your private and public key files\n";
echo "2. Upload the key files to var/mypos/ directory\n";
echo "3. Set proper file permissions: chmod 600 var/mypos/private_key.pem\n";
echo "4. Configure the payment method in Magento admin\n";
echo "5. Test in sandbox mode first\n";
echo "6. Run the validation script: php Test/validate_installation.php\n\n";

echo "🔗 MyPOS Support Contact:\n";
echo "   Website: https://mypos.com/\n";
echo "   Email: <EMAIL>\n";
echo "   Phone: Check MyPOS website for current contact information\n\n";

echo "📧 When contacting MyPOS support, mention:\n";
echo "   - You need private and public key files for API integration\n";
echo "   - Your client number: " . MyPOS_Config_Setup::CLIENT_NUMBER . "\n";
echo "   - You're integrating with Magento 1.9\n";
echo "   - You need confirmation of Store ID vs Wallet Number mapping\n";
