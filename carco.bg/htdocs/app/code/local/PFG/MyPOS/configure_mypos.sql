-- MyPOS Payment Method Configuration SQL
-- Run this in your Magento database to configure MyPOS payment method
-- Replace 'your_database_name' with your actual database name

USE your_database_name;

-- Insert/Update MyPOS configuration values
-- Note: Adjust the website_id and store_id values according to your setup (usually 0 for default)

-- Enable MyPOS payment method
INSERT INTO core_config_data (scope, scope_id, path, value) 
VALUES ('default', 0, 'payment/pfg_mypos/active', '1')
ON DUPLICATE KEY UPDATE value = '1';

-- Set payment method title
INSERT INTO core_config_data (scope, scope_id, path, value) 
VALUES ('default', 0, 'payment/pfg_mypos/title', 'MyPOS Credit Card Payment')
ON DUPLICATE KEY UPDATE value = 'MyPOS Credit Card Payment';

-- Set instructions
INSERT INTO core_config_data (scope, scope_id, path, value) 
VALUES ('default', 0, 'payment/pfg_mypos/instructions', 'You will be redirected to MyPOS secure payment page to complete your payment.')
ON DUPLICATE KEY UPDATE value = 'You will be redirected to MyPOS secure payment page to complete your payment.';

-- Set description
INSERT INTO core_config_data (scope, scope_id, path, value) 
VALUES ('default', 0, 'payment/pfg_mypos/description', 'Payment via MyPOS gateway')
ON DUPLICATE KEY UPDATE value = 'Payment via MyPOS gateway';

-- Set Store ID (SID) - YOUR CLIENT NUMBER (confirmed from MyPOS docs)
INSERT INTO core_config_data (scope, scope_id, path, value)
VALUES ('default', 0, 'payment/pfg_mypos/store_id', 'HSdDQ8dSl9MVzoMbGxgRVLyt')
ON DUPLICATE KEY UPDATE value = 'HSdDQ8dSl9MVzoMbGxgRVLyt';

-- Set Client Number - YOUR CLIENT CODE (this is the second credential)
INSERT INTO core_config_data (scope, scope_id, path, value)
VALUES ('default', 0, 'payment/pfg_mypos/client_number', 'DfqEPIicwSZPl7hQw4ONay6SlE6BUtAjKgnBu0DVJIdwsrZB')
ON DUPLICATE KEY UPDATE value = 'DfqEPIicwSZPl7hQw4ONay6SlE6BUtAjKgnBu0DVJIdwsrZB';

-- Set currency
INSERT INTO core_config_data (scope, scope_id, path, value) 
VALUES ('default', 0, 'payment/pfg_mypos/currency', 'BGN')
ON DUPLICATE KEY UPDATE value = 'BGN';

-- Set language
INSERT INTO core_config_data (scope, scope_id, path, value) 
VALUES ('default', 0, 'payment/pfg_mypos/language', 'BG')
ON DUPLICATE KEY UPDATE value = 'BG';

-- Set private key path
INSERT INTO core_config_data (scope, scope_id, path, value) 
VALUES ('default', 0, 'payment/pfg_mypos/private_key_path', 'mypos/private_key.pem')
ON DUPLICATE KEY UPDATE value = 'mypos/private_key.pem';

-- Set public key path
INSERT INTO core_config_data (scope, scope_id, path, value) 
VALUES ('default', 0, 'payment/pfg_mypos/public_key_path', 'mypos/public_key.pem')
ON DUPLICATE KEY UPDATE value = 'mypos/public_key.pem';

-- Set private key password (empty by default)
INSERT INTO core_config_data (scope, scope_id, path, value) 
VALUES ('default', 0, 'payment/pfg_mypos/private_key_password', '')
ON DUPLICATE KEY UPDATE value = '';

-- Enable sandbox mode for testing
INSERT INTO core_config_data (scope, scope_id, path, value) 
VALUES ('default', 0, 'payment/pfg_mypos/sandbox_mode', '1')
ON DUPLICATE KEY UPDATE value = '1';

-- Set new order status
INSERT INTO core_config_data (scope, scope_id, path, value) 
VALUES ('default', 0, 'payment/pfg_mypos/new_order_status', 'pending')
ON DUPLICATE KEY UPDATE value = 'pending';

-- Set order status after payment
INSERT INTO core_config_data (scope, scope_id, path, value) 
VALUES ('default', 0, 'payment/pfg_mypos/order_status_after_payment', 'processing')
ON DUPLICATE KEY UPDATE value = 'processing';

-- Set sort order
INSERT INTO core_config_data (scope, scope_id, path, value) 
VALUES ('default', 0, 'payment/pfg_mypos/sort_order', '110')
ON DUPLICATE KEY UPDATE value = '110';

-- Verify the configuration
SELECT 
    path,
    value,
    scope,
    scope_id
FROM core_config_data 
WHERE path LIKE 'payment/pfg_mypos/%' 
ORDER BY path;

-- Show current payment methods
SELECT 
    path,
    value
FROM core_config_data 
WHERE path LIKE 'payment/%/active' AND value = '1'
ORDER BY path;
