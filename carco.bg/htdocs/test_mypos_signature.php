<?php
/**
 * MyPOS Signature Test Script
 * This script tests the MyPOS signature generation using the same data from the logs
 */

// Include Magento
require_once 'app/Mage.php';
Mage::app();

// Test data from the logs (line 1190-1218)
$testData = array(
    'AccountSettlement' => '',
    'Amount' => '2549',
    'CartItems' => '[{"name":"\u0412\u044a\u043d\u0448\u043d\u0430 \u0434\u0440\u044a\u0436\u043a\u0430 \u043f\u0440\u0435\u0434\u043d\u0430 \u043b\u044f\u0432\u0430 \u0437\u0430 ALFA ROMEO 166 \u0441\u0435\u0434\u0430\u043d (936) 2.4 JTD (936A2A__) (1998 - 2007)","quantity":1,"price":2549}]',
    'Currency' => 'BGN',
    'IPCLanguage' => 'BG',
    'IPCVersion' => '1.4',
    'IPCmethod' => 'IPCPurchase',
    'Note' => 'Test payment method description showing up',
    'OrderID' => '100120988_1753908588',
    'OutputFormat' => 'json',
    'SID' => '1124357',
    'URL_Cancel' => 'http://localhost/pfg_mypos/response/cancel/?SID=b2522bf8cd87f0ae18ba0c8025c76d83',
    'URL_Notify' => 'http://localhost/pfg_mypos/response/notify/?SID=b2522bf8cd87f0ae18ba0c8025c76d83',
    'URL_OK' => 'http://localhost/pfg_mypos/response/success/?SID=b2522bf8cd87f0ae18ba0c8025c76d83',
    'WalletNumber' => '***********',
    'customeremail' => '<EMAIL>',
    'customerfirstname' => 'test',
    'customerlastname' => 'test',
    'customerphone' => '**********',
    'keyindex' => '1'
);

echo "=== MyPOS Signature Test ===\n\n";

// Test 1: Current implementation (with empty AccountSettlement)
echo "1. CURRENT IMPLEMENTATION (with empty AccountSettlement):\n";
echo "Data: " . print_r($testData, true) . "\n";

ksort($testData);
$values = array();
foreach ($testData as $key => $value) {
    $values[] = (string)$value;
}

$concatenated = implode('-', $values);
$base64String = base64_encode($concatenated);

echo "Values: " . print_r($values, true) . "\n";
echo "Concatenated: " . $concatenated . "\n";
echo "Base64: " . $base64String . "\n\n";

// Test 2: Without AccountSettlement (exclude empty values)
echo "2. WITHOUT EMPTY AccountSettlement:\n";
$testData2 = $testData;
unset($testData2['AccountSettlement']); // Remove empty AccountSettlement

ksort($testData2);
$values2 = array();
foreach ($testData2 as $key => $value) {
    $values2[] = (string)$value;
}

$concatenated2 = implode('-', $values2);
$base64String2 = base64_encode($concatenated2);

echo "Values: " . print_r($values2, true) . "\n";
echo "Concatenated: " . $concatenated2 . "\n";
echo "Base64: " . $base64String2 . "\n\n";

// Test 3: MyPOS Documentation Example Format
echo "3. MYPOS DOCUMENTATION FORMAT:\n";
// According to MyPOS docs: concatenate all values with dashes, then Base64 encode
// But they might expect non-empty values only

$testData3 = $testData;
// Remove empty values
foreach ($testData3 as $key => $value) {
    if ($value === '' || $value === null) {
        unset($testData3[$key]);
    }
}

ksort($testData3);
$values3 = array();
foreach ($testData3 as $key => $value) {
    $values3[] = (string)$value;
}

$concatenated3 = implode('-', $values3);
$base64String3 = base64_encode($concatenated3);

echo "Values (non-empty only): " . print_r($values3, true) . "\n";
echo "Concatenated: " . $concatenated3 . "\n";
echo "Base64: " . $base64String3 . "\n\n";

// Test signature generation
$privateKeyPath = 'var/mypos/private_key.pem';
if (file_exists($privateKeyPath)) {
    echo "4. SIGNATURE GENERATION TEST:\n";
    
    $privateKey = file_get_contents($privateKeyPath);
    $privateKeyResource = openssl_get_privatekey($privateKey);
    
    if ($privateKeyResource) {
        // Test with current format
        openssl_sign($base64String, $signature1, $privateKeyResource, OPENSSL_ALGO_SHA256);
        $signature1_b64 = base64_encode($signature1);
        
        // Test without empty values
        openssl_sign($base64String3, $signature3, $privateKeyResource, OPENSSL_ALGO_SHA256);
        $signature3_b64 = base64_encode($signature3);
        
        echo "Signature (with empty AccountSettlement): " . $signature1_b64 . "\n";
        echo "Signature (without empty values): " . $signature3_b64 . "\n";
        
        openssl_free_key($privateKeyResource);
    } else {
        echo "Failed to load private key\n";
    }
} else {
    echo "Private key file not found: $privateKeyPath\n";
}

echo "\n=== Test Complete ===\n";

// Test 5: Generate signature for exact POST data from logs
echo "5. GENERATE SIGNATURE FOR EXACT POST DATA:\n";

// Use the exact data from your working POST request (but with corrected signature)
$exactPostData = array(
    'AccountSettlement' => '',
    'Amount' => '2549',
    'CartItems' => '[{"name":"\u0412\u044a\u043d\u0448\u043d\u0430 \u0434\u0440\u044a\u0436\u043a\u0430 \u043f\u0440\u0435\u0434\u043d\u0430 \u043b\u044f\u0432\u0430 \u0437\u0430 ALFA ROMEO 166 \u0441\u0435\u0434\u0430\u043d (936) 2.4 JTD (936A2A__) (1998 - 2007)","quantity":1,"price":2549}]',
    'Currency' => 'BGN',
    'IPCLanguage' => 'BG',
    'IPCVersion' => '1.4',
    'IPCmethod' => 'IPCPurchase',
    'Note' => 'Test payment method description showing up',
    'OrderID' => '100120988_1753908588',
    'OutputFormat' => 'json',
    'SID' => '1124357',
    'URL_Cancel' => 'http://localhost/pfg_mypos/response/cancel/?SID=b2522bf8cd87f0ae18ba0c8025c76d83',
    'URL_Notify' => 'http://localhost/pfg_mypos/response/notify/?SID=b2522bf8cd87f0ae18ba0c8025c76d83',
    'URL_OK' => 'http://localhost/pfg_mypos/response/success/?SID=b2522bf8cd87f0ae18ba0c8025c76d83',
    'WalletNumber' => '***********',
    'customeremail' => '<EMAIL>',
    'customerfirstname' => 'test',
    'customerlastname' => 'test',
    'customerphone' => '**********',
    'keyindex' => '1'
);

// Test different signature approaches
echo "Testing different signature approaches:\n";

// Approach 1: Exclude empty AccountSettlement completely
$signatureData1 = $exactPostData;
unset($signatureData1['AccountSettlement']);
ksort($signatureData1);
$signatureValues1 = array_values($signatureData1);
$signatureConcatenated1 = implode('-', $signatureValues1);
$signatureBase64_1 = base64_encode($signatureConcatenated1);
echo "Approach 1 (exclude empty): " . $signatureBase64_1 . "\n";

// Approach 2: Include empty AccountSettlement as empty string
$signatureData2 = $exactPostData;
ksort($signatureData2);
$signatureValues2 = array_values($signatureData2);
$signatureConcatenated2 = implode('-', $signatureValues2);
$signatureBase64_2 = base64_encode($signatureConcatenated2);
echo "Approach 2 (include empty): " . $signatureBase64_2 . "\n";

// Use approach 1 for now
$signatureData = $signatureData1;
$signatureValues = $signatureValues1;
$signatureConcatenated = $signatureConcatenated1;
$signatureBase64 = $signatureBase64_1;

$signatureConcatenated = implode('-', $signatureValues);
$signatureBase64 = base64_encode($signatureConcatenated);

echo "Signature Values: " . print_r($signatureValues, true) . "\n";
echo "Signature Concatenated: " . $signatureConcatenated . "\n";
echo "Signature Base64: " . $signatureBase64 . "\n";

// Generate the actual signature
if (file_exists('var/mypos/private_key.pem')) {
    $privateKey = file_get_contents('var/mypos/private_key.pem');
    $privateKeyResource = openssl_get_privatekey($privateKey);

    if ($privateKeyResource) {
        openssl_sign($signatureBase64, $finalSignature, $privateKeyResource, OPENSSL_ALGO_SHA256);
        $finalSignatureB64 = base64_encode($finalSignature);

        echo "Final Signature: " . $finalSignatureB64 . "\n";

        // Now prepare POST data with the correct signature
        $postData = $exactPostData;
        $postData['Signature'] = $finalSignatureB64;

        openssl_free_key($privateKeyResource);
    } else {
        echo "Failed to load private key\n";
        $postData = null;
    }
} else {
    echo "Private key file not found\n";
    $postData = null;
}

if ($postData) {

    echo "POST Data: " . print_r($postData, true) . "\n";

    // Send the request
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://mypos.com/vmp/checkout-test');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    echo "HTTP Code: $httpCode\n";
    if ($error) {
        echo "cURL Error: $error\n";
    }

    // Check if we get a redirect (success) or error response
    if ($httpCode == 302 || $httpCode == 301) {
        echo "SUCCESS: MyPOS accepted the signature! (Redirect response)\n";
    } elseif ($httpCode == 200) {
        // Check if response contains error
        if (strpos($response, 'E_SIGNATURE_FAILED') !== false) {
            echo "ERROR: Signature still failed\n";
            // Extract JSON response
            $bodyStart = strpos($response, "\r\n\r\n");
            if ($bodyStart !== false) {
                $body = substr($response, $bodyStart + 4);
                echo "Response Body: $body\n";
            }
        } else {
            echo "SUCCESS: MyPOS accepted the request!\n";
        }
    } else {
        echo "Response: " . substr($response, 0, 1000) . "...\n";
    }
} else {
    echo "No signature generated for testing\n";
}

echo "\n=== Test Complete ===\n";
?>
